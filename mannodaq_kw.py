from __future__ import annotations

import json
import re
from datetime import datetime
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from Emulator import Emulator
import time

from error import log, setup_logger
from schemas import CountryCodeEnum, PhoneRequest, RequestStatusEnum, SearchTypeEnum
from Task import AppStateBase, Task
from utils import format_xpath_attrs, load_config, validate_word


CONFIG = load_config()
DEBUGGING = False


class MannodaqKwAppState(AppStateBase):
    # App States
    PERMISSIONS_REQUEST = "permissions_request"
    INTRO_SCREEN = "intro_screen"
    SEARCH_SCREEN = "search_screen"
    RESULTS_SCREEN = "results_screen"


class MannodaqKw(Task):
    AppState = MannodaqKwAppState
    XPATH_PATTERNS = {
        AppState.PERMISSIONS_REQUEST: [
            format_xpath_attrs(
                "com.android.permissioncontroller:id/permission_allow_button"
            )
        ],
        AppState.INTRO_SCREEN: [
            "//node[contains(@content-desc, 'التالي') or contains(@content-desc, '       دخول       ')]",
        ],
        AppState.SEARCH_SCREEN: [
            format_xpath_attrs(content_desc="بحث بالرقم"),
            format_xpath_attrs(content_desc="بحث بالاسم"),
        ],
        AppState.RESULTS_SCREEN: [
            format_xpath_attrs(content_desc="نتائج بحث"),
        ],
    }

    def __init__(
        self,
        emulator: Emulator,
        request: PhoneRequest,
    ):
        self.logger = setup_logger(
            f"Manno_Daq_{emulator.device_name}",
            f"logs/Manno_Daq_{emulator.device_name}.log",
        )
        super().__init__(
            emulator,
            request,
        )
        self.state_handlers = {
            self.AppState.PERMISSIONS_REQUEST: self.simple_state_handler,
            self.AppState.INTRO_SCREEN: self.intro_screen_handler,
            self.AppState.SEARCH_SCREEN: self.handle_search_screen,
            self.AppState.UNKNOWN: self.handle_unknown_state,
        }
        self.results = {}
        self.done_automation = False

    def intro_screen_handler(self):
        for _ in range(5):
            content = self.emulator.get_lxml_tree()
            if content.xpath(self.XPATH_PATTERNS[self.AppState.INTRO_SCREEN][0]):
                self.simple_state_handler()
        return True

    def check_quota(self, content):
        quota_xpath = format_xpath_attrs(content_desc="باقي بحث مجاني")
        quota = content.xpath(quota_xpath)
        text = quota[0].get("content-desc")
        num = re.findall(r"\d+", text)
        if num:
            return int(num[0])
        return 0

    def handle_search_screen(self):
        content = self.emulator.get_lxml_tree()

        if self.check_quota(content) < 10:
            self.emulator.clear_data(self.package_name)
            time.sleep(2)
            self.emulator.open_app(self.package_name)
            time.sleep(5)
            return
        by_number = format_xpath_attrs(content_desc="بحث بالرقم")
        by_name = format_xpath_attrs(content_desc="بحث بالاسم")
        search_field_xpath = format_xpath_attrs(**{"class": "android.widget.EditText"})

        if self.reqeust.search_type == SearchTypeEnum.ByNumber:
            self.emulator.click_by_xpath(content, by_number)
        else:
            self.emulator.click_by_xpath(content, by_name)

        time.sleep(1)
        self.emulator.click_by_xpath_direct(content, search_field_xpath)
        time.sleep(1)
        self.emulator.click_by_xpath_direct(content, search_field_xpath)
        time.sleep(1)
        self.emulator.clear_text_adb_keyboard()
        self.emulator.type_text_adb_keyboard(self.reqeust.query)
        time.sleep(3)
        search_button_xpath = format_xpath_attrs(**{"class": "android.widget.Button"})
        # 2 is the index of the search button
        
        self.emulator.click_by_xpath_direct(content, search_button_xpath)
        time.sleep(5)
        content = self.emulator.get_lxml_tree()

        if content.xpath(self.XPATH_PATTERNS[self.AppState.RESULTS_SCREEN][0]):
            self.results = self.scroll_and_fetch_names()
            self.done_automation = True
            self.emulator.click_back_button()
        else:
            self.done_automation = True
            self.results = {}
            # there is no results for this number
            # maybe make a warning message here

        return True

    def get_content_desc(self, elements):
        if not len(elements):
            return [elements.get('content-desc')] if elements.get('content-desc') else []
        
        ret = []
        for element in elements:
            ret += self.get_content_desc(element)

        return ret
    
    def is_right_format(self, item : str) -> bool:
        if not len(item):
            return False
        
        list = item.split('\n')

        if len(list) != 2:
            return False
        
        for digit in list[1]:
            if digit not in "+0987654321":
                return False
        
        if 'اشترك' in item:
            return False
            
        return True

    def check_words(self, item : str) -> bool:
        words = item.split()
        for word in words:
            if not validate_word(word):
                return False
        
        return True        
            
    def scroll_and_fetch_names(self):
        log(self.emulator.main_logger, "debug", "getting results")

        stop = False
        returned_results = {}

        while not stop:
            try:
                stop = True
                content = self.emulator.get_lxml_tree()
                elements = content.xpath("//node[@class = 'android.widget.ScrollView']")[0]

                for element in elements:
                    rets = self.get_content_desc(element)
                    for ret in rets:
                        if (ret not in returned_results.keys() and 
                            self.is_right_format(ret) and 
                            self.check_words(ret)):

                            stop = False
                            returned_results[ret] = 1
                
                self.emulator.scroll_down()
                time.sleep(1.5)
            except Exception as e:
                log(self.emulator.main_logger, "debug", f"error occured while getting results: {str(e)} !!")

        
        try:                
            if self.reqeust.search_type == SearchTypeEnum.ByName:
                returned_results = {element.split('\n')[1]:"" for element in returned_results}
            else:
                returned_results = {element.split('\n')[0]:"" for element in returned_results}
        except Exception as e:
            log(self.emulator.main_logger, "debug", f"error occured while perpering results: {str(e)} !!")


        return returned_results

    def handle_unknown_state(self):
        content = self.emulator.get_lxml_tree()

        if len(content.xpath("//node")) == 1:
            # this case when the adb keyboard overlays the app
            self.emulator.click_back_button()
            return
        return super().handle_unknown_state()

    @staticmethod
    def get_name():
        return "Mannodaq_Kuwait"

    @staticmethod
    def get_package_name():
        return "app.menodag.spamkiller"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        return [CountryCodeEnum.KUWAIT]

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        return [SearchTypeEnum.ByNumber, SearchTypeEnum.ByName]


def testing():
    from Emulator import Emulator

    e = Emulator(1, "FA79V1A06540")
    # queries = ["69088885", "mohammed"]

    # e = Emulator(1, "0B121JECB05419")
    queries = ["zakaria", "69088885", "22970301", "61667530", "67757762", "mohammad", "obaida"]
    reqs = [
        PhoneRequest(
            id=i,
            search_type=(SearchTypeEnum.ByNumber
                            if x[0].isdigit()
                            else SearchTypeEnum.ByName),
            country_code=CountryCodeEnum.KUWAIT.value,
            query=x,
            status=RequestStatusEnum.NEW,
            created_at=datetime.now(),
            modified_at=datetime.now(),
        )
        for i, x in enumerate(queries)
    ]
    for req in reqs:
        mno = MannodaqKw(e, req)
        mno.run_automation()
        print(f"fetched {len(mno.results)} results for {req.query}")
        with open(f"results_{int(time.time())}.json", "w") as f:
            json.dump(mno.results, f, indent=4, ensure_ascii=False)


if __name__ == "__main__":
    testing()
