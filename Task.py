from __future__ import annotations

import time
from abc import ABC, abstractmethod
from collections import deque

# from enum import Enum
from pathlib import Path
from traceback import format_exc
from typing import TYPE_CHECKING, Callable

from lxml import etree

from error import log, setup_logger
from schemas import CountryCodeEnum, PhoneRequest, SearchTypeEnum

if TYPE_CHECKING:
    from Emulator import Emulator


class FailedAutomationException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class RateLimitException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class NotFoundException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class NotClickableException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class AppErrorException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class UnknownAppState(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class CountryCodeNotFoundException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class AppStateBase:
    UNKNOWN = "unknown"


class Task(ABC):
    XPATH_PATTERNS = {}
    AppState = AppStateBase
    logger = setup_logger("Task", "logs/Task.log")
    STUCK_STATE_THRESHOLD = 5
    STUCK_STATE_BACK_ATTEMPTS = 2
    CONSECUTIVE_FAILURES_THRESHOLD = 3
    MAX_RETRIES = 2
    RETRY_DELAY = 5
    TIMEOUT = 120

    def __init__(
        self,
        emulator: Emulator,
        request: PhoneRequest,
        clear_data_on_first_start: bool = True,
        max_retries: int = MAX_RETRIES,
        retry_delay: int = RETRY_DELAY,
        timeout: int = TIMEOUT,
    ):
        self.name = self.get_name()
        self.clear_data_on_first_start = clear_data_on_first_start
        self.package_name = self.get_package_name()
        self.emulator = emulator
        self.current_region = None
        self.reqeust = request
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.current_state = None
        self.previous_states = deque(maxlen=15)  # Fixed-size deque
        self.state_transition_times = {}  # Track when we entered each state
        self.state_handlers = {
            self.AppState.UNKNOWN: self.handle_unknown_state,
        }
        self.results = {}
        self.done_automation = False

    def cleared_app_data_init(self):
        self.emulator.clear_data(self.package_name)
        self.emulator.open_app(self.package_name)
        time.sleep(10)

    @abstractmethod
    def scroll_and_fetch_names(self, phone):
        return {}

    def handle_unknown_state(self) -> bool:
        try:
            # Get current UI content
            content = self.emulator.get_lxml_tree()

            # Check for "app isn't responding" dialog
            anr_title_xpath = "//node[contains(@text, 'not responding') or contains(@text, 'has stopped')]"
            close_app_xpath = "//node[contains(@resource-id, 'android:id/aerr_close') or contains(@text, 'Close app')]"

            anr_title = content.xpath(anr_title_xpath)
            if anr_title:
                log(
                    self.logger,
                    "WARNING",
                    "Detected 'App isn't responding' dialog, attempting to close app",
                    screenshot=self.emulator.take_screenshot(),
                    xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                    device=self.emulator.full_name_writable,
                )

                # Try to find and click the "Close app" button
                close_button = content.xpath(close_app_xpath)
                if close_button:
                    button_bounds = self.emulator.get_bounds_by_xpath(
                        content, close_app_xpath
                    )
                    self.emulator.click_element_by_bounds(button_bounds)
                    log(
                        self.logger,
                        "INFO",
                        "Clicked 'Close app' button to close non-responsive app",
                        device=self.emulator.full_name_writable,
                    )
                    time.sleep(2)
                    # Don't reopen the app, just return
                    return True

            # If not an "app isn't responding" dialog, proceed with regular unknown state handling
            log(
                self.logger,
                "WARNING",
                f"Unknown state: {self.current_state}",
                screenshot=self.emulator.take_screenshot(),
                xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                device=self.emulator.full_name_writable,
            )
            self.emulator.click_back_button()
            # ensure that it is not outside the app
            self.emulator.open_app(self.get_package_name())
            time.sleep(5)
        except Exception as e:
            # Log the exception but don't re-raise it
            log(
                self.logger,
                "ERROR",
                f"Error in handle_unknown_state: {str(e)}",
                exc_info=True,
                device=self.emulator.full_name_writable,
            )
        return True

    def simple_state_handler(self):
        """
        A simple handler that clicks the button associated with the current state.
        """
        assert self.current_state, "No current state to handle."
        self.logger.debug(f"Handling {self.current_state}")
        xpath = self.XPATH_PATTERNS[self.current_state][0]
        content = self.emulator.get_lxml_tree()
        button_bounds = self.emulator.get_bounds_by_xpath(content, xpath)
        self.emulator.click_element_by_bounds(button_bounds)
        time.sleep(3)
        return True

    def press_back_handler(self):
        self.emulator.click_back_button()
        time.sleep(1)
        return True

    def focus(self):
        self.emulator.open_phone()
        self.emulator.open_app(self.package_name)

    def focus_and_run(self):
        self.focus()
        self.run()

    def run(self):
        raise NotImplementedError

    def run_state_based(self):
        self.emulator.open_app(self.get_package_name())
        self.emulator.empty_ram_with_adb()
        try:
            self.run_automation()
        finally:
            try:
                self.emulator.force_stop_app(self.get_package_name())
            except Exception as e:
                pass
        if not self.done_automation:
            raise FailedAutomationException
        return self.results

    def determine_current_state(self, xml_content: etree.ElementBase) -> AppStateBase:
        """
        Determines the current state based on the provided XML content,
        updates state history, and records the transition time.
        """
        if self.current_state:
            self.previous_states.append(self.current_state)

        # Detect new state
        try:
            new_state = self.detect_state(xml_content)
        except Exception as e:
            log(
                self.logger,
                "ERROR",
                f"Error detecting state: {str(e)}",
                exc_info=True,
                xml_log=self.emulator.xml_logger(
                    etree.tostring(xml_content, encoding="utf-8").decode("utf-8")
                ),
                screenshot=self.emulator.take_screenshot(),
                device=self.emulator.full_name_writable,
            )
            raise
        self.current_state = new_state

        # Record transition time
        self.state_transition_times[new_state] = time.time()
        log(
            self.logger,
            "INFO",
            f"Current state: {new_state}",
            device=self.emulator.full_name_writable,
        )
        return new_state

    def process_current_state(self) -> bool:
        """
        Processes the current state using the appropriate handler.
        """
        if not self.current_state or self.current_state not in self.state_handlers:
            log(
                self.logger,
                "WARNING",
                f"No handler for state: {self.current_state}",
                screenshot=self.emulator.take_screenshot(),
                xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                device=self.emulator.full_name_writable,
            )
            return False
        handler = self.state_handlers[self.current_state]
        return self._execute_with_retry(handler)

    def _execute_with_retry(self, func: Callable, *args, **kwargs) -> bool:
        """
        Executes a function with retry logic and exponential backoff.
        """
        attempts = 0
        while attempts < self.max_retries:
            try:
                result = func(*args, **kwargs)
                return result
            except Exception:
                attempts += 1
                delay = self.retry_delay + attempts * 2
                log(
                    self.logger,
                    "WARNING",
                    f"Operation failed: {format_exc()}. Retrying in {delay}s ({attempts}/{self.max_retries})",
                    xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                    screenshot=self.emulator.take_screenshot(),
                    device=self.emulator.full_name_writable,
                )
                time.sleep(delay)
        self.logger.error(f"Operation failed after {self.max_retries} attempts")
        return False

    def is_state_stuck(self, consecutive_threshold: int = 5) -> bool:
        """
        Checks if the current state is stuck by comparing with previous states.

        Args:
            consecutive_threshold: Number of consecutive identical states to consider as stuck
                                  Default is 5 iterations

        Returns:
            bool: True if the same state has been repeated for consecutive_threshold times
        """
        if len(self.previous_states) < consecutive_threshold:
            return False

        # Get the last N states
        last_states = list(self.previous_states)[-consecutive_threshold:]

        # Check if all states are the same as current state
        is_stuck = all(state == self.current_state for state in last_states)

        if is_stuck:
            # Calculate how long we've been in this state
            time_in_state = 0
            if self.current_state in self.state_transition_times:
                time_in_state = (
                    time.time() - self.state_transition_times[self.current_state]
                )

            # Log detailed information about the stuck state
            last_ten = min(10, len(self.previous_states))
            state_history = (
                ", ".join(list(self.previous_states)[-last_ten:])
                if len(self.previous_states) > 0
                else "None"
            )
            log(
                self.logger,
                "WARNING",
                f"Detected stuck state: {self.current_state}, "
                f"consecutive occurrences: {consecutive_threshold}, "
                f"time in state: {time_in_state:.2f}s, "
                f"recent state history: {state_history}",
                device=self.emulator.full_name_writable,
            )

        return is_stuck

    def _handle_stuck_state(self, max_back_attempts: int = 3, back_delay: int = 2):
        """
        Attempts to recover from a stuck state.

        First tries clicking the back button multiple times, then restarts the app if needed.

        Args:
            max_back_attempts: Maximum number of times to press back button
            back_delay: Delay in seconds between back button presses

        Returns:
            bool: True if recovery appears successful
        """
        log(
            self.logger,
            "INFO",
            f"Attempting to recover from stuck state: {self.current_state}",
            device=self.emulator.full_name_writable,
        )

        # Try pressing back button multiple times
        for attempt in range(1, max_back_attempts + 1):
            log(
                self.logger,
                "INFO",
                f"Pressing back button (attempt {attempt}/{max_back_attempts})",
                device=self.emulator.full_name_writable,
            )
            self.emulator.click_back_button()
            time.sleep(back_delay)
            # Check if state has changed after pressing back
            try:
                xml_content = self.emulator.get_lxml_tree()
                old_state = self.current_state
                self.determine_current_state(xml_content)
                if (
                    old_state != self.current_state
                    and self.current_state != self.AppState.UNKNOWN
                ):
                    log(
                        self.logger,
                        "INFO",
                        f"Successfully navigated back to state: {self.current_state}",
                        device=self.emulator.full_name_writable,
                    )
                    return True
            except Exception:
                pass  # Continue with recovery if we can't check the state

        # If back button doesn't work, restart the app
        log(
            self.logger,
            "INFO",
            "Attempting to restart app",
            device=self.emulator.full_name_writable,
        )
        self.emulator.force_stop_app(self.get_package_name())
        time.sleep(3)
        return self.emulator.open_app(self.get_package_name())

    def check_timeout(self, state: str) -> bool:
        """
        Checks if the elapsed time in the given state has exceeded the timeout.
        """
        if state not in self.state_transition_times:
            return False
        time_in_state = time.time() - self.state_transition_times[state]
        return time_in_state > self.timeout

    def run_automation(self, max_iterations: int = 30):
        """
        Runs the main automation loop with improved flow control.
        Iterates until the automation is complete or the maximum iterations are reached.
        """
        iteration = 0
        consecutive_failures = 0
        while iteration < max_iterations:
            if consecutive_failures >= self.CONSECUTIVE_FAILURES_THRESHOLD:
                log(
                    self.logger,
                    "ERROR",
                    "Too many consecutive failures, aborting "
                    f"automation after {self.CONSECUTIVE_FAILURES_THRESHOLD} failures",
                    screenshot=self.emulator.take_screenshot(),
                    xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                    device=self.emulator.full_name_writable,
                )
                return

            self.logger.info(f"Automation iteration {iteration + 1}/{max_iterations}")

            # Get the screen XML
            try:
                xml_content = self.emulator.get_lxml_tree()
            except Exception:
                consecutive_failures += 1
                continue

            try:
                self.determine_current_state(xml_content)
                success = self.process_current_state()
            except RateLimitException as e:
                raise e
            except Exception as e:
                consecutive_failures += 1
                log(
                    self.logger,
                    "ERROR",
                    f"Error in run_automation: {str(e)}",
                    exc_info=True,
                    screenshot=self.emulator.take_screenshot(),
                    xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                    device=self.emulator.full_name_writable,
                )
                continue
            # Check if we've reached our goal state
            if not success:
                consecutive_failures += 1

            if self.done_automation:
                log(
                    self.logger,
                    "INFO",
                    "Successfully done the automation",
                    device=self.emulator.full_name_writable,
                )
                return
            # Handle failure cases
            if self.is_state_stuck(self.STUCK_STATE_THRESHOLD):
                log(
                    self.logger,
                    "WARNING",
                    f"Failed to process state or stuck in state: {self.current_state}",
                    screenshot=self.emulator.take_screenshot(),
                    xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                    device=self.emulator.full_name_writable,
                )
                self._handle_stuck_state(self.STUCK_STATE_BACK_ATTEMPTS)

            # Check for timeout
            if self.check_timeout(self.current_state):
                log(
                    self.logger,
                    "WARNING",
                    f"Timeout in state {self.current_state}",
                    screenshot=self.emulator.take_screenshot(),
                    xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
                    device=self.emulator.full_name_writable,
                )
                self._handle_stuck_state()
                consecutive_failures += 2  # it's more weighted
            iteration += 1
            time.sleep(1)  # Add a small delay between iterations
        log(
            self.logger,
            "WARNING",
            f"Automation did not complete within {max_iterations} iterations",
            screenshot=self.emulator.take_screenshot(),
            xml_log=self.emulator.xml_logger(self.emulator.get_ui_hierarchy()),
            device=self.emulator.full_name_writable,
        )
        return

    @classmethod
    def detect_state(cls, xml_content: etree.ElementBase) -> AppStateBase:
        """
        Determines the current state based on the XML content.
        Checks each state's patterns and returns the first matching state.
        """
        for state, patterns in cls.XPATH_PATTERNS.items():
            for pattern in patterns:
                if xml_content.xpath(pattern):
                    return state
        return AppStateBase.UNKNOWN

    @classmethod
    @abstractmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def install_current_app(cls, device: Emulator):
        apk_dir = Path("apks")
        files = [str(x) for x in apk_dir.iterdir()]
        pkg = cls.get_package_name()
        prfx = "apks/"
        if (prfx + pkg) not in files and (prfx + pkg + ".apk") not in files:
            raise Exception(f"app: {pkg} not found!")
        tmp = Path(f"apks/{pkg}")
        if tmp.exists() and tmp.is_dir():
            apks = filter(lambda x: x.name.endswith(".apk"), tmp.iterdir())
            device.install_multi_app([str(x) for x in apks])
        else:
            device.install_app(f"apks/{cls.get_package_name()}.apk")

    @staticmethod
    @abstractmethod
    def get_name() -> str:
        pass

    @staticmethod
    @abstractmethod
    def get_package_name() -> str:
        pass

    @classmethod
    @abstractmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        raise NotImplementedError

    @classmethod
    def not_supported_regions(cls) -> list[CountryCodeEnum]:
        """
        Use this when selecting all regions, but with exception of some regions
        """
        return []

    @classmethod
    @abstractmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        raise NotImplementedError

    @classmethod
    def can_handle_request(cls, request: PhoneRequest) -> bool:
        if request.country_code in CountryCodeEnum._value2member_map_.keys():
            cc_type = CountryCodeEnum(request.country_code)
        else:
            cc_type = CountryCodeEnum.ALL
        return (
            (
                CountryCodeEnum.ALL in cls.supported_regions()
                and cc_type not in cls.not_supported_regions()
            )
            or cc_type in cls.supported_regions()
        ) and request.search_type in cls.supported_search_types()

    @classmethod
    def requires_root(cls) -> bool:
        return False

    @classmethod
    def requires_non_root(cls) -> bool:
        return False
