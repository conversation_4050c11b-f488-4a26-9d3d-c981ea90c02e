from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class SearchTypeEnum(Enum):
    ByNumber = "ByNumber"
    ByName = "ByName"
    ByEmail = "ByEmail"


class CountryCodeEnum(Enum):
    ALL = "all"
    KUWAIT = "965"
    QATAR = "974"
    SAUDI_ARABIA = "966"
    UAE = "971"
    BAHRAIN = "973"
    OMAN = "968"
    EGYPT = "20"
    ALGERIA = "213"
    IRAQ = "964"
    JORDON = "962"
    LEBANON = "961"
    LIBYA = "218"
    MOROCCO = "212"
    PALASTINE = "970"
    SUDAN = "249"
    SYRIA = "963"
    TUNISIA = "216"
    MAURITANIA = "222"    
    FALKLAND_ISLANDS = "500"
    AFGHANISTAN = "93"
    CAMBODIA = "855"
    LUXEMBOURG = "352"
    CHINA = "86"
    SOMALIA = "252"
    BANGLADESH = "880"
    ZAMBIA = "260"
    PHILIPPINES = "63"
    SRI_LANKA = "94"

class RequestStatusEnum(Enum):
    NEW = "new"
    PENDING = "pending"
    FAILED = "failed"
    UNCOMPLETED = "uncompleted"
    SUCCESS = "success"


class ScrapingStatusEnum(Enum):
    SUCCESS = "success"
    FAILD = "faild"
    LIMIT_REACHED = "limit_reached"


class PhoneRequest(BaseModel):
    id: int
    search_type: SearchTypeEnum
    country_code: CountryCodeEnum | str
    query: str = Field(max_length=255, min_length=3)
    is_paid_user: bool = Field(default=False)
    status: RequestStatusEnum
    retries: int = 0
    created_at: datetime
    modified_at: datetime
    parent: "PhoneRequest | None" = None
    apps: list[str] | None = Field(default=[])
