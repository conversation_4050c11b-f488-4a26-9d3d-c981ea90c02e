from __future__ import annotations

from typing import TYPE_CHECKING, Callable

if TYPE_CHECKING:
    from Emulator import Emulator
import time
from logging import Logger

from error import log
from hello_caller.config import DEBUG<PERSON><PERSON>, get_google_accounts
from schemas import PhoneRequest, SearchTypeEnum
from Task import NotFoundException, NotClickableException, AppErrorException
import base64

USERNAME, EMAIL, PASSWORD = get_google_accounts()

class StateHandler:
    def __init__(self, emulator: Emulator, logger: Logger, request: PhoneRequest, set_done_automation: Callable[[], None]):
        self.emulator = emulator
        self.logger = logger
        self.request = request
        self.new_names = set()
        self.set_done_automation = set_done_automation

    def choose_language(self) -> bool:
        try:
            print("choose_language")
            content = self.emulator.get_lxml_tree()

            # First verify we're actually on the language selection screen
            language_container_xpath = (
                "//node[@resource-id='com.callerid.wie:id/lnLanguages']"
            )
            if not self.emulator.get_bounds_by_xpath(content, language_container_xpath):
                if DEBUGGING:
                    log(self.logger, "error", "Not on language selection screen", True)
                return False

            # XPath patterns for language buttons
            english_button_xpath = (
                "//node[@resource-id='com.callerid.wie:id/btnFirstLanguage']"
            )
            arabic_button_xpath = (
                "//node[@resource-id='com.callerid.wie:id/btnSecondLanguage']"
            )

            # Try to find and click the English button first
            english_button = self.emulator.get_bounds_by_xpath(
                content, english_button_xpath
            )
            if english_button:
                if DEBUGGING:
                    log(self.logger, "info", "Selecting English language", False)
                self.emulator.click_element_by_bounds(english_button)
                time.sleep(2)
                return True

            # If English button not found, try Arabic button
            arabic_button = self.emulator.get_bounds_by_xpath(content, arabic_button_xpath)
            if arabic_button:
                if DEBUGGING:
                    log(self.logger, "info", "Selecting Arabic language", False)
                self.emulator.click_element_by_bounds(arabic_button)
                time.sleep(2)
                return True

            if DEBUGGING:
                log(self.logger, "error", "No language buttons found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Language selection failed: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Language button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in choose_language: {str(e)}", True)
            return False

    def handle_privacy_policy(self) -> bool:
        try:
            print("handle_privacy_policy")
            content = self.emulator.get_lxml_tree()

            agree_button_xpath = "//node[@resource-id='com.callerid.wie:id/chAgree']"
            agree_button = self.emulator.get_bounds_by_xpath(content, agree_button_xpath)

            continue_button_xpath = "//node[@resource-id='com.callerid.wie:id/btnContinue']"
            continue_button = self.emulator.get_bounds_by_xpath(
                content, continue_button_xpath
            )

            if agree_button and continue_button:
                if DEBUGGING:
                    log(self.logger, "info", "Agree button found", False)
                self.emulator.click_element_by_bounds(agree_button)
                time.sleep(2)
                if DEBUGGING:
                    log(self.logger, "info", "Continue button found", False)
                self.emulator.click_element_by_bounds(continue_button)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Agree button not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Privacy policy elements not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Privacy policy buttons not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in handle_privacy_policy: {str(e)}", True)
            return False

    def press_skip(self) -> bool:
        try:
            print("press_skip")
            content = self.emulator.get_lxml_tree()
            skip_button_xpath = "//node[@resource-id='com.callerid.wie:id/btnSkip']"
            skip_button = self.emulator.get_bounds_by_xpath(content, skip_button_xpath)
            if skip_button:
                if DEBUGGING:
                    log(self.logger, "info", "Skip button found", False)
                self.emulator.click_element_by_bounds(skip_button)
                time.sleep(2)
                return True
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Skip button not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Skip button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in press_skip: {str(e)}", True)
            return False

    def enter_email(self) -> bool:
        global USERNAME, EMAIL, PASSWORD
        USERNAME, EMAIL, PASSWORD = get_google_accounts()
        try:
            print("enter_email")
            content = self.emulator.get_lxml_tree()
            email_input_xpath = "//node[@resource-id='com.callerid.wie:id/etEmail']"
            email_input = self.emulator.get_bounds_by_xpath(content, email_input_xpath)
            if email_input:
                if DEBUGGING:
                    log(self.logger, "info", "Email input found", False)
                self.emulator.click_element_by_bounds(email_input)
                time.sleep(2)
                self.emulator.clear_text_adb_keyboard()
                # self.emulator.clear_all_text()
                time.sleep(2)
                for c in EMAIL:
                    self.emulator.type_text(c)
                    time.sleep(0.2)
                time.sleep(2)

            self.emulator.click_back_button()
            content = self.emulator.get_lxml_tree()
            continue_button_xpath = "//node[@resource-id='com.callerid.wie:id/btnContinue']"
            continue_button = self.emulator.get_bounds_by_xpath(
                content, continue_button_xpath
            )
            if continue_button:
                if DEBUGGING:
                    log(self.logger, "info", "Continue button found", False)
                self.emulator.click_element_by_bounds(continue_button)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Email input or continue button not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Email input elements not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Email input elements not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in enter_email: {str(e)}", True)
            return False

    def continue_with_google(self) -> bool:
        try:
            print("continue_with_google")
            content = self.emulator.get_lxml_tree()
            google_button_xpath = "//node[@resource-id='com.callerid.wie:id/btnGoogle']"
            google_button = self.emulator.get_bounds_by_xpath(content, google_button_xpath)
            if google_button:
                if DEBUGGING:
                    log(self.logger, "info", "Google button found", False)
                self.emulator.click_element_by_bounds(google_button)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Google button not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Google button not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Google button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in continue_with_google: {str(e)}", True)
            return False

    def choose_google_account(self) -> bool:
        try:
            print("choose_google_account")
            res = self.find_and_click_email_container()
            if res:
                return True
            content = self.emulator.get_lxml_tree()
            email_xpath = (
                f"//node[contains(@resource-id, 'com.google.android.gms:id/container')]"
            )
            email = self.emulator.get_bounds_by_xpath(content, email_xpath)
            if email:
                if DEBUGGING:
                    log(self.logger, "info", "Email found", False)
                self.emulator.click_element_by_bounds(email)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Email not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Google account not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Google account not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in choose_google_account: {str(e)}", True)
            return False

    def handle_skip(self) -> bool:
        try:
            print("handle_skip")
            content = self.emulator.get_lxml_tree()
            skip_button_xpath = "//node[@resource-id='com.callerid.wie:id/btnSkipForNow']"
            skip_button = self.emulator.get_bounds_by_xpath(content, skip_button_xpath)
            if skip_button:
                if DEBUGGING:
                    log(self.logger, "info", "Skip button found", False)
                self.emulator.click_element_by_bounds(skip_button)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Skip button not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Skip button not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Skip button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in handle_skip: {str(e)}", True)
            return False

    def click_on_search(self) -> bool:
        try:
            print("click_on_search")
            content = self.emulator.get_lxml_tree()
            search_button_xpath = "//node[@resource-id='com.callerid.wie:id/cardSearch']"
            search_button = self.emulator.get_bounds_by_xpath(content, search_button_xpath)
            if search_button:
                if DEBUGGING:
                    log(self.logger, "info", "Search button found", False)
                self.emulator.click_element_by_bounds(search_button)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Search button not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Search button not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Search button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in click_on_search: {str(e)}", True)
            return False

    def enter_name_and_password(self) -> bool:
        try:
            content = self.emulator.get_lxml_tree()
            name_input_xpath = "//node[@resource-id='com.callerid.wie:id/etName']"
            name_input = self.emulator.get_bounds_by_xpath(content, name_input_xpath)
            if name_input:
                if DEBUGGING:
                    log(self.logger, "info", "Name input found", False)
                self.emulator.click_element_by_bounds(name_input)
                time.sleep(2)
                self.emulator.clear_text_adb_keyboard()
                # self.emulator.clear_all_text()
                time.sleep(2)
                for c in USERNAME:
                    self.emulator.type_text(c)
                    time.sleep(0.2)
                time.sleep(2)
            else:
                if DEBUGGING:
                    log(self.logger, "error", "Name input not found", True)
                return False

            password_input_xpath = "//node[@resource-id='com.callerid.wie:id/etPassword']"
            password_input = self.emulator.get_bounds_by_xpath(
                content, password_input_xpath
            )
            if password_input:
                if DEBUGGING:
                    log(self.logger, "info", "Password input found", False)
                self.emulator.click_element_by_bounds(password_input)
                time.sleep(2)
                self.emulator.clear_text_adb_keyboard()
                # self.emulator.clear_all_text()
                time.sleep(2)

                for c in PASSWORD:
                    self.emulator.type_text(c)
                    time.sleep(0.2)
                time.sleep(2)
            else:
                if DEBUGGING:
                    log(self.logger, "error", "Password input not found", True)
                return False

            self.emulator.click_back_button()
            content = self.emulator.get_lxml_tree()
            continue_button_xpath = (
                "//node[@resource-id='com.callerid.wie:id/btnSaveAndContinue']"
            )
            continue_button = self.emulator.get_bounds_by_xpath(
                content, continue_button_xpath
            )
            if continue_button:
                if DEBUGGING:
                    log(self.logger, "info", "Continue button found", False)
                self.emulator.click_element_by_bounds(continue_button)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Continue button not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Name/password input elements not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Name/password input elements not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in enter_name_and_password: {str(e)}", True)
            return False

    def enter_password(self) -> bool:
        try:
            content = self.emulator.get_lxml_tree()
            password_input_xpath = "//node[@resource-id='com.callerid.wie:id/etPassword']"
            login_button_xpath = "//node[@resource-id='com.callerid.wie:id/btnLogin']"
            password_input = self.emulator.get_bounds_by_xpath(
                content, password_input_xpath
            )
            if password_input:
                self.emulator.click_element_by_bounds(password_input)
                time.sleep(2)
                self.emulator.clear_text_adb_keyboard()
                # self.emulator.clear_all_text()
                time.sleep(2)
                for c in PASSWORD:
                    self.emulator.type_text(c)
                    time.sleep(0.2)
                time.sleep(2)
                login_button = self.emulator.get_bounds_by_xpath(
                    content, login_button_xpath
                )
                if login_button:
                    self.emulator.click_element_by_bounds(login_button)
                    time.sleep(2)
                    return True
            if DEBUGGING:
                log(self.logger, "error", "Password input not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Password input elements not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Password input elements not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in enter_password: {str(e)}", True)
            return False

    def search_input_screen(self) -> bool:
        try:
            print("search_input_screen")
            time.sleep(2)
            content = self.emulator.get_lxml_tree()
            search_field_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/etSearch')]"
            country_code_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/rlClickConsumer')]"
            country_code_bounds = self.emulator.get_bounds_by_xpath(content, country_code_xpath)
            if country_code_bounds:
                if DEBUGGING:
                    log(self.logger, "info", "Country code found", False)
                self.emulator.click_element_by_bounds(country_code_bounds)
                time.sleep(2)
                content = self.emulator.get_lxml_tree()
                search_field_bounds = self.emulator.get_bounds_by_xpath(content, search_field_xpath)
                if search_field_bounds:
                    if DEBUGGING:
                        log(self.logger, "info", "Search field found", False)
                    self.emulator.click_element_by_bounds(search_field_bounds)
                    time.sleep(2)
                    self.emulator.clear_text_adb_keyboard()
                    # self.emulator.clear_all_text()
                    time.sleep(2)
                    self.emulator.type_text(self.request.country_code)
                    time.sleep(2)
                    country_name_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/tvCountryName')]"
                    country_name_bounds = self.emulator.get_bounds_by_xpath(content, country_name_xpath)
                    if country_name_bounds:
                        if DEBUGGING:
                            log(self.logger, "info", "Country name found", False)
                        self.emulator.click_element_by_bounds(country_name_bounds)
                        time.sleep(2)


            content = self.emulator.get_lxml_tree()
            search_field_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/etSearch')]"
            by_number_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/btnNumber')]"
            by_name_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/btnName')]"
            search_button_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/btnSearch')]"
            if self.request.search_type == SearchTypeEnum.ByNumber:
                button_bounds = self.emulator.get_bounds_by_xpath(content, by_number_xpath)
            else:
                button_bounds = self.emulator.get_bounds_by_xpath(content, by_name_xpath)
            search_bounds = self.emulator.get_bounds_by_xpath(content, search_field_xpath)
            search_button_bounds = self.emulator.get_bounds_by_xpath(content, search_button_xpath)
            if button_bounds and search_bounds and search_button_bounds:
                if DEBUGGING:
                    log(self.logger, "debug", "Clicking search type button", True)
                self.emulator.click_element_by_bounds(button_bounds)
                time.sleep(2)
                if DEBUGGING:
                    log(self.logger, "debug", "Clicking search field", True)
                self.emulator.click_element_by_bounds(search_bounds)
                time.sleep(2)
                if DEBUGGING:
                    log(self.logger, "debug", "Clearing search field", True)
                self.emulator.clear_text_adb_keyboard()
                # self.emulator.clear_all_text()
                time.sleep(2)
                if DEBUGGING:
                    log(self.logger, "debug", "Typing search query", True)
                # for t in self.request.query:
                #     self.emulator.type_text_adb_keyboard(t)
                #     # self.emulator.type_text(t)
                #     time.sleep(0.2)
                self.emulator.type_text_adb_keyboard(self.request.query)
                # self.emulator.type_text(self.request.query)
                time.sleep(2)
                if DEBUGGING:
                    log(self.logger, "debug", "Clicking search button", True)
                self.emulator.click_element_by_bounds(search_button_bounds)
                time.sleep(2)
                return True
            if DEBUGGING:
                log(self.logger, "error", "Search input screen not found", True)
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Search input elements not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Search input elements not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in search_input_screen: {str(e)}", True)
            return False
            
    def handle_copies(self) -> bool:
        try:
            print("handel_copies")
            content = self.emulator.get_lxml_tree()
            cancel_button_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/btnCancel')]"
            cancel_button_bounds = self.emulator.get_bounds_by_xpath(content, cancel_button_xpath)
            if cancel_button_bounds:
                if DEBUGGING:
                    log(self.logger, "debug", "Click Cancel button", True)
                self.emulator.click_element_by_bounds(cancel_button_bounds)
                time.sleep(2)
                return True
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Cancel button not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Cancel button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in handle_copies: {str(e)}", True)
            return False
    
    def handle_identified_the_caller(self) -> bool:
        print("handle_identified_the_caller")
        try:
            print("handle_identified_the_caller")
            content = self.emulator.get_lxml_tree()
            cancel_button_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/btnCancel')]"
            cancel_button_bounds = self.emulator.get_bounds_by_xpath(content, cancel_button_xpath)
            if cancel_button_bounds:
                if DEBUGGING:
                    log(self.logger, "debug", "Click Cancel button", True)
                self.emulator.click_element_by_bounds(cancel_button_bounds)
                time.sleep(2)
                return True
            return False
        except NotFoundException as e:
            log(self.logger, "error", f"Cancel button not found: {str(e)}", True)
            return False
        except NotClickableException as e:
            log(self.logger, "error", f"Cancel button not clickable: {str(e)}", True)
            return False
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in handle_identified_the_caller: {str(e)}", True)  

    def find_and_click_email_container(self) -> bool:
        try:
            print("find_and_click_email_container")
            content = self.emulator.get_lxml_tree()
            _, EMAIL, _ = get_google_accounts()
            # First find the email text node
            email_xpath = f"//node[@text='{EMAIL}']"
            email_node = content.xpath(email_xpath)
            if not email_node:
                if DEBUGGING:
                    log(self.logger, "error", f"Email node not found for: {EMAIL}", True)
                return False
                
            # Get the closest clickable parent node
            parent = email_node[0].getparent()
            while parent is not None:
                if parent.get('clickable') == 'true':
                    bounds = parent.get('bounds')
                    if bounds:                            
                        self.emulator.click_element_by_bounds(bounds)
                        time.sleep(2)
                        return True
                parent = parent.getparent()
                
            if DEBUGGING:
                log(self.logger, "error", "No clickable parent found for email node", True)
            return False
            
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in find_and_click_email_container: {str(e)}", True)
            return False  
    
    def change_google_accounts(self) -> bool:
        try:
            print("change_google_accounts")

            content = self.emulator.get_lxml_tree()
            text_node = content.xpath(f"//node[@text='Okay']")
            text_node_bounds = text_node[0].get('bounds')
            if not text_node_bounds:
                if DEBUGGING:
                    log(self.logger, "error", "Text node bounds not found", True)
                    raise Exception("Text node bounds not found")
            self.emulator.click_element_by_bounds(text_node_bounds)
            time.sleep(2)
            self.emulator.click_back_button()
            time.sleep(2)

            content = self.emulator.get_lxml_tree()
            settings_button_xpath = f"//node[@text='Settings']"
            settings_button_node = content.xpath(settings_button_xpath)
            if not settings_button_node:
                if DEBUGGING:
                    log(self.logger, "error", "Settings node not found", True)
                    raise Exception("Settings node not found")
            
            parent = settings_button_node[0].getparent()
            while parent is not None:
                if parent.get('clickable') == 'true':
                    bounds = parent.get('bounds')
                    if bounds:                            
                        self.emulator.click_element_by_bounds(bounds)
                        time.sleep(2)
                        break
                parent = parent.getparent()
            
            content = self.emulator.get_lxml_tree()
            logout_button_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/btnLogout')]"
            logout_button_node = self.emulator.get_bounds_by_xpath(content, logout_button_xpath)
            if not logout_button_node:
                if DEBUGGING:
                    log(self.logger, "error", "Logout button not found", True)
                    raise Exception("Logout button not found")
            self.emulator.click_element_by_bounds(logout_button_node)
            time.sleep(2)

            content = self.emulator.get_lxml_tree()
            yes_button_xpath = "//node[contains(@text, 'Yes')]"
            yes_button_node = self.emulator.get_bounds_by_xpath(content, yes_button_xpath)
            if not yes_button_node:
                if DEBUGGING:
                    log(self.logger, "error", "Yes button not found", True)
                    raise Exception("Yes button not found")
            self.emulator.click_element_by_bounds(yes_button_node)
            time.sleep(2)

            if DEBUGGING:
                log(self.logger, "error", "Settings button not found", True)
                raise Exception("Settings button not found")
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in change_google_accounts: {str(e)}", True)
            raise Exception(f"Unexpected error in change_google_accounts: {str(e)}")