#!/bin/bash

# Ensure the output directory exists
mkdir -p xml

# Get the device name from command line argument
DEVICE_NAME=$1

# Ensure a device name is provided
if [ -z "$DEVICE_NAME" ]; then
    echo "Usage: $0 <device_name>"
    exit 1
fi

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Output file with device name and timestamp
OUT_FILE="xml/screen_snapshot_${DEVICE_NAME}_$TIMESTAMP.xml"

# Dump UI hierarchy on the device
adb -s "$DEVICE_NAME" shell uiautomator dump /sdcard/window_dump.xml

# Pull the XML to your local machine
adb -s "$DEVICE_NAME" pull /sdcard/window_dump.xml "$OUT_FILE"

# Optional: Pretty-print (format) the XML using xmllint if installed
if command -v xmllint &> /dev/null; then
    xmllint --format "$OUT_FILE" -o "$OUT_FILE"
    echo "Formatted UI snapshot saved to $OUT_FILE"
else
    echo "UI snapshot saved to $OUT_FILE (unformatted — install 'xmllint' to format)"
fi
