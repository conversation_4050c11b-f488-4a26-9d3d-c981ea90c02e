import json
import logging
import os
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, Optional

import requests

# Configure logging
logger = logging.getLogger(__name__)

# API Configuration
BASE_URL = "https://api.smspool.net/"
TOKEN = os.getenv("SMSPOOL_TOKEN")


class SmsStatus(Enum):
    PENDING = 1
    EXPIRED = 2
    RECEIVED = 3
    CANCELLED = 4


@dataclass
class SmsResult:
    phone: Optional[str] = None
    order_id: Optional[str] = None
    country_code: Optional[str] = None
    cost: Optional[float] = None
    country_name: Optional[str] = None
    service_name: Optional[str] = None
    status: Optional[SmsStatus] = None
    sms_content: Optional[str] = None

    def __bool__(self) -> bool:
        return bool(self.phone)


class ApiError(Exception):
    def __init__(self, message: str, response=None, error_code=None):
        self.message = message
        self.response = response
        self.error_code = error_code
        super().__init__(self.message)


class SmsPoolApi:
    def __init__(self, token=None, base_url=None):
        self.token = token or TOKEN
        if not self.token:
            raise ValueError("SmsPool API token is required")
        self.base_url = base_url or BASE_URL
        self.payload = {"key": self.token}

    def _request(self, endpoint: str, data=None) -> Dict[str, Any]:
        """Make API request with error handling"""
        url = f"{self.base_url}{endpoint}"
        request_data = {**self.payload, **(data or {})}

        try:
            response = requests.post(url, data=request_data, timeout=30)
            response.raise_for_status()
            response_data = response.json()

            if (
                isinstance(response_data, dict)
                and response_data.get("success") is False
            ):
                error_message = response_data.get("message", "Unknown API error")
                error_code = response_data.get("error")
                logger.error(f"API error: {error_message} (code: {error_code})")
                raise ApiError(f"API error: {error_message}", response, error_code)

            return response_data

        except json.JSONDecodeError:
            logger.error(f"Failed to parse response from {endpoint}")
            raise ApiError("Invalid response from API")
        except requests.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            raise ApiError(f"Request failed: {str(e)}", getattr(e, "response", None))

    def get_balance(self) -> float:
        """Get the current account balance"""
        try:
            response = self._request("request/balance")
            return float(response["balance"])
        except (KeyError, ValueError) as e:
            logger.error(f"Failed to parse balance: {str(e)}")
            raise ApiError("Invalid balance format in response")

    def order_number(
        self, service: str, country: str, pricing_option: int = 1
    ) -> Optional[SmsResult]:
        """Order a phone number for receiving SMS"""
        try:
            response = self._request(
                "purchase/sms",
                {
                    "country": country,
                    "service": service,
                    "quantity": 1,
                    "pricing_option": pricing_option,
                },
            )

            return SmsResult(
                phone=response.get("phonenumber"),
                order_id=response.get("order_id"),
                country_code=response.get("cc"),
                cost=float(response.get("cost", 0)),
                country_name=country,
                service_name=service,
            )
        except Exception:
            logger.exception("Failed to order number")
            return None

    def cancel_order(self, order_info: SmsResult) -> bool:
        """Cancel an SMS order"""
        if not order_info.order_id:
            raise ValueError("Order ID is required")

        try:
            response = self._request("sms/cancel", {"orderid": order_info.order_id})
            return bool(response.get("success", False))
        except Exception:
            logger.exception("Failed to cancel order")
            return False

    def check_sms(self, order_info: SmsResult) -> SmsResult:
        """Check if SMS has been received"""
        if not order_info.order_id:
            raise ValueError("Order ID is required")

        try:
            response = self._request("sms/check", {"orderid": order_info.order_id})

            status_code = response.get("status")
            if status_code is not None:
                try:
                    order_info.status = SmsStatus(status_code)
                except ValueError:
                    order_info.status = None

            if order_info.status == SmsStatus.RECEIVED:
                order_info.sms_content = response.get("sms", "")

        except Exception:
            logger.exception("Failed to check SMS")

        return order_info
