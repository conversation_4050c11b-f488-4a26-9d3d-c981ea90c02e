from __future__ import annotations

import os
import random
import string
import threading
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import TYPE_CHECKING

import requests

from neo_backup import <PERSON>B<PERSON><PERSON>, NeoBackupRest<PERSON>
from smspool_api import SmsPool<PERSON><PERSON>, SmsStatus

if TYPE_CHECKING:
    from Emulator import Emulator
import re
import time

from constants import COUNTRY_NAME_MAP
from error import log, setup_logger
from schemas import CountryCodeEnum, PhoneRequest, RequestStatusEnum, SearchTypeEnum
from Task import (
    AppStateBase,
    CountryCodeNotFoundException,
    RateLimitException,
    Task,
)
from utils import (
    SERVER,
    NoBackupFoundException,
    download_and_process_backup,
    format_xpath_attrs,
    format_xpath_attrs_exact,
    get_devices,
    load_config,
    prune_phone_number,
    remove_emojis,
    validate_word,
)

CONFIG = load_config()
DEBUGGING = False
MM_BACKEND_KEY = os.getenv("MM_BACKEND_KEY")
BACKEND_IP = os.getenv("BACKEND_IP")
BACKEND_PORT = os.getenv("BACKEND_PORT")
SERVER_URL = f"http://{BACKEND_IP}:{BACKEND_PORT}"
PRODUCER = os.getenv("PRODUCER", "false").lower() == "true"
LIMIT_REACHED_WAITING = datetime.now()


class GetContactAppState(AppStateBase):
    FIRST_WELCOME_SCREEN = "first_welcome_screen"
    FEATURE_TOUR_SCREEN = "featues_tour_screen"
    ACCEPT_TERMS = "accept_terms"
    SET_DEFUALT_SCREEN = "set_default_screen"
    LOGIN_WELCOME = "login_welcome"
    EMAIL_LOGIN = "email_login"
    NUMBER_REGISTER = "number_register"
    SUBSCRIPTION_SCREEN = "subscription_screen"

    VERIFICATION_CODE = "verification_code"
    TERMS_AND_CONDITIONS = "terms_and_conditions"
    PERMISSIONS_REQUEST = "permissions_request"
    POPUP = "popup"
    MAIN_SEARCH_SCREEN = "main_screen"
    UNKNOWN = "unknown"
    RESULTS_SCREEN = "results_screen"
    NO_SEARCH_HISTORY = "no_search_history"
    RESULTS_SCREEN_BY_TAGS = "results_screen_by_tags"
    ACTIVATE_PRIVATE_MODE = "activate_private_mode"
    NOT_VISIBLE_NUMBERS = "not_visible_numbers"
    EXCEEDED_FREE_LIMIT = "exceeded_free_limit"
    UNABLE_TO_LUNCH_APP = "unable_to_lunch_app"
    ASK_FOR_COOKIES = "ask_for_cookies"


class GetContact(Task):
    AppState = GetContactAppState
    XPATH_PATTERNS = {
        AppState.NO_SEARCH_HISTORY: [
            format_xpath_attrs(text="No search history found")
        ],
        AppState.FIRST_WELCOME_SCREEN: [
            format_xpath_attrs("view.onBoarding.welcome.continueButton")
        ],
        AppState.FEATURE_TOUR_SCREEN: [
            format_xpath_attrs("view.onBoarding.featureTour.continueButton")
        ],
        AppState.ACCEPT_TERMS: [
            format_xpath_attrs("view.onBoarding.privacyAndTerms.acceptWithAgreeButton")
        ],
        AppState.SET_DEFUALT_SCREEN: [
            format_xpath_attrs("view.onBoarding.permission.defaultDialerButton"),
        ],
        AppState.LOGIN_WELCOME: [
            format_xpath_attrs("view.onBoarding.loginWelcome.signInWithMail")
        ],
        AppState.EMAIL_LOGIN: [
            format_xpath_attrs("view.onBoarding.emailLogin.fullName"),
            format_xpath_attrs("view.onBoarding.emailLogin.email"),
        ],
        AppState.NUMBER_REGISTER: [
            format_xpath_attrs("view.onBoarding.addPhone.labelCountry"),
            format_xpath_attrs("view.onBoarding.addPhone.labelPhoneNumber"),
        ],
        AppState.SUBSCRIPTION_SCREEN: [
            format_xpath_attrs("HeroAnimationFSLandingScreenCloseButton"),
            format_xpath_attrs("app.source.getcontact:id/vp_billing_activity"),
        ],
        AppState.MAIN_SEARCH_SCREEN: [
            format_xpath_attrs(
                "view.newsfeed.search.unfocus.searchhint", text="Search by number"
            ),
        ],
        AppState.RESULTS_SCREEN: [format_xpath_attrs("view.numberdetail.searchresult")],
        AppState.RESULTS_SCREEN_BY_TAGS: [
            format_xpath_attrs("view.numberdetail.searchresult.userTagAction.item")
        ],
        AppState.ACTIVATE_PRIVATE_MODE: [
            format_xpath_attrs(text="Activate Private Mode")
        ],
        AppState.NOT_VISIBLE_NUMBERS: [
            format_xpath_attrs(text="certain numbers are not visible in Getcontact")
        ],
        AppState.EXCEEDED_FREE_LIMIT: [
            format_xpath_attrs(text="You have exceeded the monthly limit"),
            format_xpath_attrs("app.source.getcontact:id/tvMessage1"),
        ],
        AppState.UNABLE_TO_LUNCH_APP: [
            format_xpath_attrs(
                text="Unfortunately, we were unable to launch Getcontact"
            )
        ],
        AppState.ASK_FOR_COOKIES: [
            format_xpath_attrs(**{"class": "android.webkit.WebView"})
        ],
    }

    def __init__(
        self,
        emulator: Emulator,
        request: PhoneRequest,
        clear_data_on_first_start=True,
        time_between_requests=15,
        wait_time_after_a_rate_limit=25,
    ):
        self.logger = setup_logger(
            f"GetContact_{emulator.device_name}",
            f"logs/GetContact_{emulator.device_name}.log",
        )
        super().__init__(
            emulator,
            request,
            clear_data_on_first_start,
            time_between_requests,
            wait_time_after_a_rate_limit,
        )
        self.state_handlers = {
            **self.state_handlers,
            self.AppState.FIRST_WELCOME_SCREEN: self.first_start_handler,
            self.AppState.FEATURE_TOUR_SCREEN: self.handle_feature_tour,
            self.AppState.ACCEPT_TERMS: self.simple_state_handler,
            self.AppState.SET_DEFUALT_SCREEN: self.press_back_handler,
            self.AppState.LOGIN_WELCOME: self.simple_state_handler,
            self.AppState.EMAIL_LOGIN: self.handle_email_login,
            self.AppState.NUMBER_REGISTER: self.handle_register_number,
            self.AppState.SUBSCRIPTION_SCREEN: self.simple_state_handler,
            self.AppState.MAIN_SEARCH_SCREEN: self.handle_search_screen,
            self.AppState.RESULTS_SCREEN: self.handle_results_screen,
            self.AppState.NO_SEARCH_HISTORY: self.handle_no_search_history,
            self.AppState.RESULTS_SCREEN_BY_TAGS: self.handle_no_search_history,
            self.AppState.ACTIVATE_PRIVATE_MODE: self.press_back_handler,
            self.AppState.NOT_VISIBLE_NUMBERS: self.handle_not_visible_numbers,
            self.AppState.EXCEEDED_FREE_LIMIT: self.handle_exceeded_free_limit,
            self.AppState.UNABLE_TO_LUNCH_APP: self.handle_unable_to_lunch_app,
            self.AppState.ASK_FOR_COOKIES: self.handle_ask_for_cookeis,
        }
        self.COUNTRY_NAME = "Colombia"
        self.number = ""

    def first_start_handler(self):
        """Handle first start of the app, including backup restoration if needed."""
        if PRODUCER:
            return self.simple_state_handler()

        global LIMIT_REACHED_WAITING
        self.emulator.clear_data(self.get_package_name())

        try:
            download_and_process_backup(
                self.emulator,
                self.get_package_name(),
                "/storage/emulated/0/backup_folder",
            )

            neobackup_restore = NeoBackupRestore(self.emulator, self.get_package_name())
            try:
                neobackup_restore.run_state_based()
            finally:
                self.emulator.force_stop_app(neobackup_restore.get_package_name())

        except NoBackupFoundException:
            log(
                self.emulator.main_logger,
                "warning",
                f"No backup found for {self.reqeust.query}",
            )
            self.done_automation = True
            LIMIT_REACHED_WAITING = datetime.now() + timedelta(hours=1)
            return

        except Exception as e:
            log(
                self.emulator.main_logger,
                "error",
                f"Error processing and uploading backup: {e}",
            )
            raise

    def handle_results_screen(self):
        content = self.emulator.get_lxml_tree()
        results_xpath = format_xpath_attrs(
            "view.numberdetail.searchresult.userTagAction.item"
        )
        title_xpath = format_xpath_attrs("view.numberdetail.profile.displayNameText")
        if content.xpath(title_xpath):
            title_text = content.xpath(title_xpath)[0].get("text", "")
            title_text = title_text.strip()
            if prune_phone_number(self.reqeust.query) not in prune_phone_number(
                title_text
            ):
                self.results[title_text] = 1
        not_visible_xpath = format_xpath_attrs(text="not visible in GetContact")
        if content.xpath(results_xpath):
            self.emulator.click_by_xpath(content, results_xpath)
            time.sleep(4)
            log(self.emulator.main_logger, "debug", "scroll_and_fetch_names")
            results = self.scroll_and_fetch_names(self.reqeust.query)
            self.results = results
        elif content.xpath(not_visible_xpath):
            self.emulator.click_back_button()
            time.sleep(1.5)

        self.done_automation = True
        for _ in range(3):
            self.emulator.click_back_button()
            time.sleep(2)
        return True

    def handle_not_visible_numbers(self):
        self.emulator.click_back_button()
        self.logger.info("Not visible numbers found for %s", self.reqeust.query)
        self.done_automation = True
        for _ in range(3):
            self.emulator.click_back_button()
            time.sleep(1.5)
        return True

    def handle_no_search_history(self):
        self.emulator.click_back_button()

        if len(self.previous_states) == 0:
            return True
        self.logger.info("No search history found for %s", self.reqeust.query)
        self.done_automation = True
        for _ in range(3):
            self.emulator.click_back_button()
            time.sleep(1.5)
        return True

    def handle_search_screen(self):
        # TODO: pass the content as a parameter, not getting new one
        content = self.emulator.get_lxml_tree()

        search_field = format_xpath_attrs("view.newsfeed.search.unfocus.searchhint")
        self.emulator.click_by_xpath(content, search_field)
        time.sleep(2)

        # choose country
        # content = self.emulator.get_lxml_tree()
        # country_button = format_xpath_attrs("view.newsfeed.search.countryselector")
        # self.emulator.click_by_xpath(content, country_button)
        # time.sleep(2)
        content = self.emulator.get_lxml_tree()
        # search_country = format_xpath_attrs(text="Select Country")

        # self.emulator.click_by_xpath(content, search_country)
        time.sleep(2)

        # country_name = COUNTRY_NAME_MAP.get(self.reqeust.country_code)
        # if not country_name:
        #     raise CountryCodeNotFoundException(
        #         f"Country name not found for country code {self.reqeust.country_code}"
        #     )
        # self.emulator.type_text_adb_keyboard(country_name)
        # time.sleep(2)
        # content = self.emulator.get_lxml_tree()
        # item_select = format_xpath_attrs("view.newsfeed.search.selectcountry.item.name")
        # self.emulator.click_by_xpath(content, item_select)
        # time.sleep(2)

        self.emulator.type_text_adb_keyboard(
            f"+{self.reqeust.country_code}{self.reqeust.query}"
        )
        time.sleep(1)
        self.emulator.click_enter()
        time.sleep(14)
        return True

    def handle_register_number(self):
        sms_api = SmsPoolApi()
        content = self.emulator.get_lxml_tree()
        naf = format_xpath_attrs(NAF="true")

        region_xpath = (
            format_xpath_attrs(**{"class": "android.widget.ScrollView"}) + naf
        )

        self.emulator.click_by_xpath_direct(content, region_xpath)

        time.sleep(5)
        content = self.emulator.get_lxml_tree()
        search_icon = format_xpath_attrs("searchicon")
        self.emulator.click_by_xpath_direct(content, search_icon)
        time.sleep(2)
        self.emulator.type_text_adb_keyboard(self.COUNTRY_NAME)
        time.sleep(3)
        content = self.emulator.get_lxml_tree()
        country_item = format_xpath_attrs("selectcountry.item.arrow")
        self.emulator.click_by_xpath_direct(content, country_item)
        time.sleep(3)
        for _ in range(5):
            content = self.emulator.get_lxml_tree()
            order_number = sms_api.order_number(
                "817", "CO"
            )  # 817 stands for not listed service
            assert order_number, "Failed to order number"
            number = order_number.phone
            full_number = f"+{order_number.country_code}{number}"
            self.emulator.change_sim_state(full_number)
            self.emulator.clear_text_adb_keyboard()
            time.sleep(1)
            self.emulator.type_text_adb_keyboard(str(number))

            time.sleep(3)
            continue_xpath = format_xpath_attrs(
                "view.onBoarding.addPhone.continueButton"
            )
            self.emulator.click_by_xpath(content, continue_xpath)
            time.sleep(35)
            content = self.emulator.get_lxml_tree()
            choose_another_method = format_xpath_attrs(text="Try another verification")
            try:
                self.emulator.click_by_xpath_direct(content, choose_another_method)
                time.sleep(10)
            except Exception as e:
                print("there is no try another verification button")

            content = self.emulator.get_lxml_tree()
            otp_xpath = format_xpath_attrs("app.source.getcontact:id/etPhoneNumber")
            self.emulator.click_by_xpath(content, otp_xpath)
            time.sleep(10)

            for _ in range(10):
                time.sleep(5)
                order_info = sms_api.check_sms(order_number)
                if order_info.status == SmsStatus.RECEIVED:
                    break
            else:
                log(
                    self.emulator.main_logger,
                    "error",
                    "OTP code not received",
                    screenshot=self.emulator.take_screenshot(),
                )
                raise Exception("OTP code not received")
            otp_code = order_info.sms_content
            if not otp_code:
                self.emulator.click_back_button()
                time.sleep(1)
                self.emulator.click_back_button()
                continue
            break

        self.emulator.type_text_adb_keyboard(otp_code)
        continue_xpath = format_xpath_attrs("app.source.getcontact:id/btnContinue")
        self.emulator.click_by_xpath(content, continue_xpath)
        time.sleep(3)
        content = self.emulator.get_lxml_tree()
        invalid_xpath = format_xpath_attrs(text="Invalid PIN")
        if content.xpath(invalid_xpath):
            self.emulator.click_back_button()
            ok_xpath = format_xpath_attrs(text="OK")
            self.emulator.click_by_xpath(content, ok_xpath)
            time.sleep(1)
            self.emulator.click_back_button()
            time.sleep(1)
            raise Exception("Invalid PIN")
        self.number = full_number
        self.send_backup()

    def extract_number(self):
        self.emulator.force_stop_app(self.get_package_name())
        time.sleep(5)
        self.emulator.open_app(self.get_package_name())
        time.sleep(15)

        content = self.emulator.get_lxml_tree()
        self.determine_current_state(content)
        state = self.current_state
        cnt = 3
        while cnt >= 0:
            time.sleep(4)
            if state == self.AppState.MAIN_SEARCH_SCREEN:
                break
            elif state in [
                self.AppState.ACTIVATE_PRIVATE_MODE,
                self.AppState.SET_DEFUALT_SCREEN,
                self.AppState.UNKNOWN,
                self.AppState.SUBSCRIPTION_SCREEN,
            ]:
                self.process_current_state()

                continue
            cnt -= 1
            self.emulator.click_back_button()
            content = self.emulator.get_lxml_tree()
            state = self.detect_state(content)
        else:
            raise Exception("Failed to extract number")

        menu_button = format_xpath_attrs_exact(text="Menu")
        self.emulator.click_by_xpath_direct(content, menu_button)
        time.sleep(2)
        content_str = str(self.emulator.get_ui_hierarchy())
        pattern = r"\+\d{10,15}\b"
        matches = re.findall(pattern, content_str)

        if len(matches) != 1:
            raise ValueError(
                "Text must contain exactly one valid international phone number."
            )
        return matches[0]

    def send_backup(self, number: str | None = None):
        """Send backup of the app data to the server."""
        number = number if number else self.number
        neobackup = NeoBackup(
            self.emulator, self.get_package_name(), {"number": number}
        )
        self.emulator.force_stop_app(neobackup.get_package_name())
        try:
            neobackup.run_state_based()
        finally:
            self.emulator.force_stop_app(neobackup.get_package_name())
        assert neobackup.done_automation, "NeoBackup failed to run"
        self.done_automation = True

    def handle_email_login(self):
        content = self.emulator.get_lxml_tree()

        fullname_xpath = format_xpath_attrs("view.onBoarding.emailLogin.fullName")

        email_xpath = format_xpath_attrs("view.onBoarding.emailLogin.email")
        continue_xpath = format_xpath_attrs("view.onBoarding.emailLogin.continueButton")
        skip_xpath = format_xpath_attrs("view.onBoarding.emailLogin.skipButton")
        username = self.gen_username()

        self.emulator.click_by_xpath(content, fullname_xpath)
        time.sleep(1)
        self.emulator.clear_text_adb_keyboard()
        time.sleep(1)
        self.emulator.type_text_adb_keyboard(username)
        time.sleep(1.5)
        self.emulator.click_by_xpath(content, email_xpath)
        time.sleep(1.5)
        try:
            self.emulator.clear_text_adb_keyboard()
        except Exception as e:
            pass
        time.sleep(2)
        # screen to google login will appear now
        self.emulator.click_back_button()
        time.sleep(2)
        self.emulator.type_text_adb_keyboard(f"{username}@yahoo.com")
        time.sleep(1)
        self.emulator.click_by_xpath(content, continue_xpath)
        # Tap on the "E-mail" field
        time.sleep(20)
        content = self.emulator.get_lxml_tree()
        self.emulator.click_by_xpath(content, skip_xpath)
        time.sleep(2)
        return True

    def handle_feature_tour(self):
        # TODO: pass the content as a parameter, not getting new one
        content = self.emulator.get_lxml_tree()
        continue_button = format_xpath_attrs(
            "view.onBoarding.featureTour.continueButton"
        )

        continue_bounds = self.emulator.get_bounds_by_xpath(content, continue_button)
        for _ in range(4):
            self.emulator.click_element_by_bounds(continue_bounds)
            time.sleep(2)
        return True

    def scroll_and_fetch_names(self, phone: str) -> list[dict[str, int]]:
        log(self.emulator.main_logger, "debug", "getting results")

        all_results_of_number = {}
        self.true_loop_start_time = time.perf_counter()

        # Track previous results count to detect when we've reached the end
        last_results_count = 0
        unchanged_results_count = 0

        content = self.emulator.get_lxml_tree()

        while True:
            # print("all_results_of_number:", all_results_of_number)
            res_xpath = format_xpath_attrs(**{"class": "android.widget.TextView"})
            result_nodes = content.xpath(res_xpath)

            for node in result_nodes:
                name = remove_emojis(node.get("text", ""))
                if not validate_word(name):
                    continue
                if (
                    "Tag(s)" in name
                    or "Hidden Tags" in name
                    or "No results found" in name
                ):
                    continue
                all_results_of_number[name] = all_results_of_number.get(name, 0) + 1

            # Get current count of results after processing this screen
            current_results_count = len(all_results_of_number)

            # Check if we've had the same number of results after scrolling
            if current_results_count == last_results_count:
                unchanged_results_count += 1
                log(
                    self.emulator.main_logger,
                    "debug",
                    f"Results unchanged: {unchanged_results_count} scrolls with {current_results_count} results",
                )
            else:
                unchanged_results_count = 0
                log(
                    self.emulator.main_logger,
                    "debug",
                    f"Found new results: {current_results_count} total (was {last_results_count})",
                )

            # Update the last results count for the next iteration
            last_results_count = current_results_count

            # If results haven't changed after 3 scrolls, we've likely read all content
            if unchanged_results_count >= 3:
                log(
                    self.emulator.main_logger,
                    "debug",
                    f"Results unchanged after 3 scrolls, ending with {current_results_count} results",
                )
                break

            # Scroll down to get more results
            self.emulator.scroll_down(600)
            time.sleep(1.5)

            # Check if scrolling revealed new content
            content = self.emulator.get_lxml_tree()

            # Safety timeout
            if time.perf_counter() - self.true_loop_start_time > 2700:
                log(
                    self.emulator.main_logger,
                    "warning",
                    "Timeout reached while fetching names",
                )
                raise Exception("timed out while getting names")

        return all_results_of_number

    def handle_ask_for_cookeis(self):
        self.emulator.force_stop_app(self.get_package_name())
        time.sleep(2)
        self.emulator.open_app(self.get_package_name())
        time.sleep(2)

    def ensure_backup_account(self):
        try:
            self.logger.info("Extracting number from device")
            number = self.extract_number()
            self.logger.info(f"Successfully extracted number: {number}")

            try:
                if self.is_stored_in_server(number):
                    self.logger.info(f"Number {number} is already stored in server")
                    return

                self.logger.info(f"Number {number} not found in server, sending backup")
                self.send_backup(number)
                self.logger.info(f"Successfully sent backup for number: {number}")

            except Exception as e:
                self.logger.error(
                    f"Failed to check server storage or send backup for number {number}: {e}"
                )

        except Exception as e:
            self.logger.error(f"Failed to extract number from device: {e}")

    def is_stored_in_server(self, number: str):
        endpoint = "api/check-number-exists"
        url = f"{SERVER}/{endpoint}"
        res = requests.post(
            url,
            json={"number": number},
            headers={"Authorization": MM_BACKEND_KEY},
            timeout=30,
        )
        return res.json()["exists"]

    def handle_exceeded_free_limit(self):
        self.logger.info("Exceeded free limit for %s", self.emulator.full_name_writable)
        self.emulator.clear_data(self.get_package_name())
        time.sleep(5)
        self.emulator.open_app(self.get_package_name())
        return True

    def handle_unable_to_lunch_app(self):
        self.emulator.force_stop_app(self.get_package_name())
        time.sleep(5)
        self.emulator.open_app(self.get_package_name())
        time.sleep(5)
        return True

    def gen_username(self) -> str:
        username = "John_{}_Doe{}".format(
            "".join(random.choices(string.ascii_lowercase, k=random.randint(2, 6))),
            (random.randint(101, 1001)),
        )
        return username

    def run_state_based(self):
        if LIMIT_REACHED_WAITING > datetime.now():
            raise RateLimitException
        return super().run_state_based()

    @staticmethod
    def get_name():
        return "GetContact"

    @staticmethod
    def get_package_name():
        return "app.source.getcontact"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        return [CountryCodeEnum.ALL]

    @classmethod
    def not_supported_regions(cls) -> list[CountryCodeEnum]:
        return [
            CountryCodeEnum.SYRIA,
            CountryCodeEnum.MAURITANIA,
            CountryCodeEnum.FALKLAND_ISLANDS,
            CountryCodeEnum.AFGHANISTAN,
            CountryCodeEnum.CAMBODIA,
            CountryCodeEnum.LUXEMBOURG,
            CountryCodeEnum.LIBYA,
            CountryCodeEnum.CHINA,
            CountryCodeEnum.SOMALIA,
            CountryCodeEnum.BANGLADESH,
            CountryCodeEnum.ZAMBIA,
            CountryCodeEnum.PALASTINE,
            CountryCodeEnum.SRI_LANKA,
        ]

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        return [SearchTypeEnum.ByNumber]

    @classmethod
    def requires_root(cls) -> bool:
        return True


def ensure_backup_all_devices():
    """
    Get all available devices and ensure backup account for each one using GetContact.
    """
    from Emulator import Emulator

    logger = setup_logger(
        "ensure_backup_all_devices", "logs/ensure_backup_all_devices.log"
    )

    try:
        # Get all available devices
        devices = get_devices()

        if not devices:
            log(logger, "warning", "No devices found")
            return

        log(logger, "info", f"Found {len(devices)} devices: {devices}")

        # Process each device in parallel
        threads = []

        def process_device(device_index, device_name):
            """Process a single device for backup"""
            try:
                log(
                    logger,
                    "info",
                    f"Processing device {device_index + 1}/{len(devices)}: {device_name}",
                )

                # Create Emulator instance
                emulator = Emulator(device_index + 1, device_name)

                # Create a dummy PhoneRequest for the GetContact instance
                # We need this for the constructor but won't actually use it for searching
                dummy_request = PhoneRequest(
                    id=device_index,  # Use device_index for unique IDs
                    search_type=SearchTypeEnum.ByNumber,
                    country_code=CountryCodeEnum.KUWAIT.value,
                    query="********",  # Dummy query
                    status=RequestStatusEnum.NEW,
                    created_at=datetime.now(),
                    modified_at=datetime.now(),
                )

                # Create GetContact instance
                get_contact = GetContact(
                    emulator, dummy_request, clear_data_on_first_start=False
                )

                # Ensure backup account for this device
                get_contact.ensure_backup_account()

                log(logger, "info", f"Successfully processed device: {device_name}")

            except Exception as e:
                log(
                    logger,
                    "error",
                    f"Error processing device {device_name}: {e}",
                    exc_info=True,
                )

        # Create and start threads for each device
        for device_index, device_name in enumerate(devices):
            thread = threading.Thread(
                target=process_device,
                args=(device_index, device_name),
                name=f"BackupDevice-{device_name}",
            )
            threads.append(thread)
            thread.start()
            log(logger, "info", f"Started backup thread for device: {device_name}")

        # Wait for all threads to complete
        log(
            logger,
            "info",
            f"Waiting for all {len(threads)} backup threads to complete...",
        )
        for thread in threads:
            thread.join()
            log(logger, "debug", f"Thread {thread.name} completed")

        log(logger, "info", f"Completed processing all {len(devices)} devices")

    except Exception as e:
        log(logger, "error", f"Error in ensure_backup_all_devices: {e}", exc_info=True)


def testing():
    from Emulator import Emulator

    # e = Emulator(1, "************:5555")
    e = Emulator(1, "localhost:5556")
    e.open_phone()
    # proxy_setup(e, "kw")
    queries = [
        "22055070",
        "52527216",
        "1832255",
        "99119311",
        "69088885",
        "67757762",
        "22970301",
        "61667530",
    ]
    with_tags = ["67770210", "96656625"]
    reqs = [
        PhoneRequest(
            id=i,
            search_type=SearchTypeEnum.ByNumber,
            country_code=CountryCodeEnum.KUWAIT.value,
            query=x,
            status=RequestStatusEnum.NEW,
            created_at=datetime.now(),
            modified_at=datetime.now(),
        )
        for i, x in enumerate(queries)
    ]
    global gc
    for req in reqs:
        gc = GetContact(e, req)
        gc.number = "+573018456664"
        gc.run_state_based()
        # e.open_app(gc.get_package_name())
        # gc.run_automation()
        # gc.handle_results_screen()
        print(gc.results)


if __name__ == "__main__":
    ensure_backup_all_devices()
