import subprocess
import time
from threading import Thread

from error import log, setup_logger

docker_logger = setup_logger("docker_restarter", "logs/docker_restarter.log")

DOCKER_RESTARTING = False  # Global flag


def get_running_containers() -> list[str]:
    result = subprocess.run(["docker", "ps", "-q"], capture_output=True, text=True)
    return result.stdout.strip().splitlines()


def get_all_containers() -> list[str]:
    result = subprocess.run(["docker", "ps", "-aq"], capture_output=True, text=True)
    return result.stdout.strip().splitlines()


def wait_for_redroid_devices(min_devices=3, timeout=120):
    from utils import get_devices

    start = time.time()
    while time.time() - start < timeout:
        active = get_devices()
        redroid_like = [d for d in active if d.startswith("localhost:")]
        if len(redroid_like) >= min_devices:
            log(docker_logger, "INFO", f"{len(redroid_like)} Redroid devices ready.")
            return True
        time.sleep(5)
    log(docker_logger, "WARNING", "Timeout waiting for Redroid devices to come online.")
    return False


def restart_docker_containers_every_six_hours() -> None:
    global DOCKER_RESTARTING

    log(
        docker_logger,
        "INFO",
        "Docker restarter will wait 6 hours before first restart.",
    )
    time.sleep(21600)  # ✅ Wait 6 hours before first restart

    while True:
        try:
            DOCKER_RESTARTING = True
            log(docker_logger, "INFO", "Restarting Docker containers...")

            running = get_running_containers()
            all_containers = get_all_containers()

            if running:
                subprocess.run(["docker", "stop"] + running, check=True)
                log(docker_logger, "INFO", "Stopped running containers.")
                time.sleep(5)

            if all_containers:
                subprocess.run(["docker", "start"] + all_containers, check=True)
                log(docker_logger, "INFO", "Started all containers.")
            else:
                log(docker_logger, "INFO", "No containers found to start.")

            log(docker_logger, "INFO", "Waiting for Redroid devices to become ready...")
            wait_for_redroid_devices()

            subprocess.run(["bash", "~/redroid-manager/adb_connect.sh"], check=True)
            log(docker_logger, "INFO", "Executed adb_connect.sh script.")

            log(docker_logger, "INFO", "Docker containers restart cycle completed.")

        except subprocess.CalledProcessError as e:
            log(docker_logger, "ERROR", f"Error restarting Docker containers: {e}")

        DOCKER_RESTARTING = False
        time.sleep(21600)  # ✅ Repeat every 6 hours



def start_docker_restart_thread() -> Thread:
    thread = Thread(
        target=restart_docker_containers_every_six_hours,
        daemon=True,
        name="DockerRestarter",
    )
    thread.start()
    return thread
