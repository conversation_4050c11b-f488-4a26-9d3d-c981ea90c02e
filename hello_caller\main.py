from schemas import PhoneRe<PERSON>, RequestStatusEnum, SearchTypeEnum, CountryCodeEnum
from hello_caller.hello_caller import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from Emulator import Emulator
from hello_caller.config import QUERIES, DEVICE_SERIAL_ID, SERVER_URL, MM_BACKEND_KEY
from typing import List
import requests

def fetch_requests() -> List[PhoneRequest]:
    url = SERVER_URL + "/api/phone-numbers"
    num_request = requests.get(
        url, headers={"Authorization": MM_BACKEND_KEY}
    )
    reqs = num_request.json()["data"]

    requests_list = []
    for req in reqs:
        requests_list.append(
            PhoneRequest(
                id=req["id"],
                search_type=SearchTypeEnum.ByName,
                country_code=req["country_code"],
                query=req["phone"],
                status=RequestStatusEnum.NEW,
                is_paid_user=req["is_premium"],
                created_at=datetime.now(),
                modified_at=datetime.now(),
            )
        )
    return requests_list


def testing() -> None:
    emulator = Emulator(1, "11163553B6007132")
    # requests = fetch_requests()
    requests = [
        PhoneRequest(
            id=1, 
            search_type=SearchTypeEnum.ByName, 
            country_code=CountryCodeEnum.KUWAIT.value,
            query="Muawiya", 
            status=RequestStatusEnum.NEW, 
            is_paid_user=True, 
            created_at=datetime.now(), 
            modified_at=datetime.now()
        ),
        PhoneRequest(
            id=2, 
            search_type=SearchTypeEnum.ByNumber, 
            country_code=CountryCodeEnum.KUWAIT.value,
            query="94845546", 
            status=RequestStatusEnum.NEW, 
            is_paid_user=True, 
            created_at=datetime.now(), 
            modified_at=datetime.now()
        ) 

    ]
    for index, request in enumerate(requests):
        print(f"Running automation for request {index + 1} of {len(requests)} {request.query}")
        hello_caller = HelloCaller(emulator, request)
        hello_caller.run_automation()
        print(hello_caller.results)

def main() -> None:
    testing()

if __name__ == "__main__":
    main()
