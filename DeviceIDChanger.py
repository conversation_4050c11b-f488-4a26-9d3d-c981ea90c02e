import re
import subprocess
import time
from pathlib import Path

from Emulator import Emulator


class DeviceIDChanger:
    """Class to handle device ID changing operations for Android apps."""

    # Constants for app identifiers
    DEVICE_ID_CHANGER_PACKAGE = "com.silverlab.app.deviceidchanger.free"
    DEVICE_ID_CHANGER_APK = "deviceidchanger.apk"

    # XPath constants for UI elements
    XPATHS = {
        "welcome_ok": "//node[@resource-id='com.silverlab.app.deviceidchanger.free:id/confirm_button']",
        "spinner": "//node[@resource-id='com.silverlab.app.deviceidchanger.free:id/spin_src']",
        "app_name": "//node[@resource-id='com.silverlab.app.deviceidchanger.free:id/name' and @text='{package}']",
        "app_name_partial": "//node[@resource-id='com.silverlab.app.deviceidchanger.free:id/name' and contains(@text, 'cuutt')]",
        "random_id": "//node[@resource-id='com.silverlab.app.deviceidchanger.free:id/fab']",
        "fast_reboot": "//node[@resource-id='com.silverlab.app.deviceidchanger.free:id/btn_fast_restart']",
        "remaining_searches": "//node[contains(@content-desc, 'Remaining Searches')]",
    }

    def __init__(
        self, emulator: Emulator, target_package: str, target_apk: str
    ) -> None:
        """Initialize the DeviceIDChanger with an emulator instance.

        Args:
            emulator: An instance of the Emulator class to interact with the Android device.
            target_package: The package name of the target app to change device ID for.
            target_apk: The name of the target app's APK file.
        """
        self.emulator = emulator
        self.target_package = target_package
        self.target_apk = target_apk
        self.apk_path = Path("apks") / self.DEVICE_ID_CHANGER_APK
        self.target_apk_path = Path("apks") / self.target_apk

    def install_app_if_needed(self) -> None:
        """Install the Device ID Changer app if it's not already installed."""
        print(f"Checking if '{self.DEVICE_ID_CHANGER_PACKAGE}' is installed...")

        if self._is_app_installed():
            print("App is already installed.")
            return

        self._install_app()
        self._verify_installation()

    def _is_app_installed(self) -> bool:
        """Check if the app is already installed.

        Returns:
            bool: True if the app is installed, False otherwise.
        """
        installed_apps = self.emulator.get_third_party_apps()
        return self.DEVICE_ID_CHANGER_PACKAGE in installed_apps

    def _install_app(self) -> None:
        """Install the app from the APK file."""
        if not self.apk_path.exists():
            raise FileNotFoundError(
                f"APK file not found at {self.apk_path}. Please ensure it exists in the 'apks' directory."
            )

        apk_path_str = str(self.apk_path.absolute())
        if not apk_path_str.endswith(".apk"):
            raise RuntimeError(f"Invalid APK filename: {apk_path_str}")

        result = self.emulator.install_app(apk_path_str)
        print(f"Install result: {result}")
        if "Success" not in result:
            raise RuntimeError("APK installation failed.")

        print("APK installation successful.")
        time.sleep(2)

    def _verify_installation(self) -> None:
        """Verify that the app was installed successfully."""
        installed_apps = self.emulator.get_third_party_apps()
        if self.DEVICE_ID_CHANGER_PACKAGE not in installed_apps:
            raise RuntimeError(
                "Installation verification failed - app not found in installed apps"
            )
        print("Installation verified successfully")

    def find_and_click_element(self, xpath: str, element_name: str) -> None:
        """Find an element by XPath and click it.

        Args:
            xpath: The XPath expression to find the element.
            element_name: A descriptive name for the element for logging purposes.
        """
        content = self.emulator.get_lxml_tree()
        node = content.xpath(xpath)

        if not node:
            raise RuntimeError(f"{element_name} not found")

        bounds = node[0].get("bounds")
        print(f"Clicking on {element_name}...")
        self.emulator.click_element_by_bounds(bounds)
        time.sleep(2)

    def select_app_from_dropdown(self) -> None:
        """Select the target app from the dropdown menu."""
        self._open_dropdown()
        self._select_app()

    def _open_dropdown(self) -> None:
        """Open the app selection dropdown."""
        self.find_and_click_element(self.XPATHS["spinner"], "spinner")
        time.sleep(1)

    def _select_app(self) -> None:
        """Select the target app from the dropdown."""
        # Try exact match first
        app_name_xpath = self.XPATHS["app_name"].format(package=self.target_package)
        content = self.emulator.get_lxml_tree()
        app_name_node = content.xpath(app_name_xpath)

        # If exact match fails, try partial match
        if not app_name_node:
            app_name_node = content.xpath(self.XPATHS["app_name_partial"])
            if not app_name_node:
                raise RuntimeError(
                    f"App name {self.target_package} not found in dropdown"
                )

        bounds = app_name_node[0].get("bounds")
        print(f"Clicking on {self.target_package}...")
        self.emulator.click_element_by_bounds(bounds)
        time.sleep(2)

    def change_device_id(self) -> None:
        """Change the device ID for the target app."""
        self._click_random_id()
        self._confirm_device_id_change()

    def _click_random_id(self) -> None:
        """Click the Random ID button."""
        self.find_and_click_element(self.XPATHS["random_id"], "Random ID")

    def _confirm_device_id_change(self) -> None:
        """Confirm the device ID change in the success dialog."""
        self.find_and_click_element(self.XPATHS["welcome_ok"], "OK in success dialog")

    def perform_fast_reboot(self) -> None:
        """Perform a fast reboot of the device."""
        self._initiate_fast_reboot()
        self._confirm_fast_reboot()
        self._wait_for_reboot()

    def _initiate_fast_reboot(self) -> None:
        """Click the Fast Reboot button."""
        self.find_and_click_element(self.XPATHS["fast_reboot"], "Fast Reboot")

    def _confirm_fast_reboot(self) -> None:
        """Confirm the fast reboot in the confirmation dialog."""
        self.find_and_click_element(
            self.XPATHS["welcome_ok"], "OK in Fast Reboot confirmation dialog"
        )

    def _wait_for_reboot(self) -> None:
        """Wait for the device to complete rebooting."""
        time.sleep(20)

    def open_app_after_clear(self) -> None:
        """Clear app data and open the app with initial setup."""
        print("Clearing app data and reopening...")
        self.emulator.clear_data(self.DEVICE_ID_CHANGER_PACKAGE)
        time.sleep(1)
        self.emulator.open_app(self.DEVICE_ID_CHANGER_PACKAGE)
        time.sleep(2)
        self._dismiss_welcome_screen()

    def _dismiss_welcome_screen(self) -> None:
        """Click OK on the welcome screen."""
        self.find_and_click_element(self.XPATHS["welcome_ok"], "OK on welcome screen")
        time.sleep(2)

    def should_change_device_id(self) -> bool:
        """Determine if the device ID should be changed based on remaining searches."""
        # Example logic: Check if remaining searches are below a threshold
        remaining_searches_xpath = self.XPATHS["remaining_searches"]
        content = self.emulator.get_lxml_tree()
        node = content.xpath(remaining_searches_xpath)

        if not node:
            print("Could not determine remaining searches.")
            return False

        remaining_searches_text = node[0].get("content-desc", "")
        match = re.search(r"\d+", remaining_searches_text)
        remaining_searches = int(match.group()) if match else 0

        # Define a threshold for changing the device ID
        threshold = 10
        return remaining_searches < threshold

    def restart_docker_if_localhost(self) -> None:
        """Restart Docker container if the device name is in localhost:<port> format."""
        device_name = self.emulator.device_name

        if device_name.startswith("localhost:"):
            port = device_name.split(":")[1]

            try:
                result = subprocess.run(
                    ["docker", "ps", "--format", "{{.ID}} {{.Names}} {{.Ports}}"],
                    check=True,
                    capture_output=True,
                    text=True,
                )

                target_container_id = None
                target_container_name = None

                for line in result.stdout.strip().splitlines():
                    parts = line.split(None, 2)
                    if len(parts) != 3:
                        continue

                    container_id, container_name, port_mapping = parts

                    if re.search(rf"0\.0\.0\.0:{port}->5555/tcp", port_mapping):
                        target_container_id = container_id
                        target_container_name = container_name
                        break

                if not target_container_id:
                    print(f"No container found exposing host port {port}")
                    return

                print(
                    f"Restarting Docker container '{target_container_name}' (ID: {target_container_id}) for port {port}..."
                )
                subprocess.run(["docker", "stop", target_container_id], check=True)
                subprocess.run(["docker", "start", target_container_id], check=True)
                print(f"Container '{target_container_name}' restarted successfully.")
                try:
                    self.emulator.manage_device_apps(target_container_id)
                except Exception as e:
                    print(f"Failed to manage device apps: {e}")

            except subprocess.CalledProcessError as e:
                print(f"Failed to restart Docker container: {e}")

    def run(self) -> None:
        """Run the complete device ID change process."""
        try:
            self.install_app_if_needed()
            self.open_app_after_clear()
            self.select_app_from_dropdown()
            self.change_device_id()
            self.perform_fast_reboot()
        except Exception as e:
            print(f"Error during device ID change process: {str(e)}")
            raise
        finally:
            self.restart_docker_if_localhost()


def main() -> None:
    """Main entry point for the script."""
    try:
        emulator = Emulator(1, "13PACS0000159Z")
        device_id_changer = DeviceIDChanger(
            emulator=emulator,
            target_package="com.cuutt.gcc",
            target_apk="com.cuutt.gcc.apk",
        )

        # Check remaining searches and change device ID if needed
        if device_id_changer.should_change_device_id():
            print("Low remaining searches detected. Changing device ID...")
            device_id_changer.run()
        else:
            print("Sufficient remaining searches. No need to change device ID.")

    except Exception as e:
        print(f"Script failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()
