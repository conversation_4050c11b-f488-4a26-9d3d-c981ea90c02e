import argparse
import json
from utils import get_devices

def main():
    parser = argparse.ArgumentParser(description="Filter devices and write to a JSON file.")
    parser.add_argument("--only-emulators", action="store_true", help="Only write emulators to the JSON file.")
    parser.add_argument("--only-physical", action="store_true", help="Only write physical devices to the JSON file.")
    
    args = parser.parse_args()
    
    devices = get_devices()
    devices = {
            "pdevices": [device for device in devices if "emulator" not in device and ":" not in device] if not args.only_emulators else [],
            "emulators": [device for device in devices if "emulator" in device or ":" in device] if not args.only_physical else []
        }
    
    with open("devices.json", "w", encoding="utf-8") as f:
        f.write(json.dumps(devices, ensure_ascii=False, indent=4))

if __name__ == "__main__":
    main()
