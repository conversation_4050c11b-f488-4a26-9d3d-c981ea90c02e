import argparse
import json
import os
import threading
from collections import defaultdict
from os import getenv
from time import sleep
from traceback import format_exc

from dotenv import load_dotenv

from Emulator import CHANNELS, Emulator
from error import log
from utils import (
    format_xpath_attrs,
    format_xpath_attrs_exact,
    get_devices,
    get_new_devices,
)

load_dotenv()
try:
    with open("device-state.json", "r") as f:
        device_state = json.loads(f.read())
except:
    device_state = {}


ip, port, username, password = (
    getenv("PROXY_IP", ""),
    getenv("PROXY_PORT", ""),
    getenv("PROXY_USERNAME", ""),
    getenv("PROXY_PASSWORD", ""),
)

proxy = [0]
INSTALL_WITH_PROXY = True
FORCE_INSTALL = False


# def old_proxy_setup(e: Emulator):
#     e.install_app("apks/proxy.apk")
#     setup_proxy(e, ip, port, username, password)
#     proxy[0] += 1
#     if e not in list(device_state.keys()):
#         device_state[e] = {"setup apps": [], "proxy user": "", "status": "init"}
#     if "proxy" not in device_state[e]["setup apps"]:
#         device_state[e]["setup apps"].append("proxy")
#     device_state[e]["proxy user"] = username


def handle_permissions(e: Emulator, package_name: str):
    perms = e.get_requested_permissions(package_name)
    for perm in perms:
        print(perm)
        e.enable_permission(package_name, perm)


def setup_adb_keyboard(e: Emulator):
    installed_apps = e.get_third_party_apps()
    if "com.android.adbkeyboard" not in installed_apps:
        e.install_app("apks/com.android.adbkeyboard.apk")
        log(e.main_logger, "INFO", "ADBKeyboard installed")
    e.exec_android_sh_command("shell ime enable com.android.adbkeyboard/.AdbIME", True)
    e.exec_android_sh_command("shell ime set com.android.adbkeyboard/.AdbIME", True)


def proxy_setup(e: Emulator, country_iso: str | None = None):
    installed_apps = e.get_third_party_apps()
    if "net.typeblog.socks" not in installed_apps:
        e.install_app("apks/net.typeblog.socks.apk")
        handle_permissions(e, "net.typeblog.socks")
    sleep(2)
    e.force_stop_app("net.typeblog.socks")
    sleep(2)
    e.open_app("net.typeblog.socks")
    sleep(3)
    content = e.get_lxml_tree()
    server_xpath = format_xpath_attrs(text="Server IP")
    ok_xpath = format_xpath_attrs(text="OK")
    if not content.xpath(format_xpath_attrs_exact(text=ip)):
        e.click_by_xpath_direct(content, server_xpath)
        sleep(2)
        e.clear_text_adb_keyboard()
        sleep(2)
        e.type_text(ip)
        e.click_by_xpath_direct(e.get_lxml_tree(), ok_xpath)
        sleep(2)
    if not content.xpath(format_xpath_attrs_exact(text=port)):
        port_xpath = format_xpath_attrs(text="Port")
        e.click_by_xpath_direct(content, port_xpath)
        sleep(2)
        e.clear_text_adb_keyboard()
        sleep(2)
        e.type_text(port)
        e.click_by_xpath_direct(e.get_lxml_tree(), ok_xpath)
        sleep(2)
    e.scroll_down(distance=500)
    sleep(3)

    username_and_pass_xpath = format_xpath_attrs(text="Password Authentication")
    # Get the parent of the username_and_pass node
    content = e.get_lxml_tree()
    username_and_pass_node = content.xpath(username_and_pass_xpath)[0]
    parent_of_username_and_pass = username_and_pass_node.getparent()

    # Get the direct sibling (brother) of the parent node
    grandparent = parent_of_username_and_pass.getparent()
    parent_index = grandparent.index(parent_of_username_and_pass)

    # Get the next sibling

    sibling_of_parent = grandparent[parent_index + 1]
    check_box_node = sibling_of_parent.getchildren()[0]

    if check_box_node.get("checked") == "false":
        checkbox_path = e.get_path_of_node(check_box_node)
        e.click_by_xpath_direct(content, checkbox_path)
        sleep(0.5)

    e.scroll_down(distance=200)
    sleep(0.5)
    content = e.get_lxml_tree()
    username_xpath = format_xpath_attrs_exact(text="Username")
    # Get the node next to (sibling of) the username xpath node
    username_node = content.xpath(username_xpath)[0]
    parent_node = username_node.getparent()

    # Find the sibling node that comes after the username node
    content_username = ""
    try:
        sibling_node = parent_node.getchildren()[1]
        content_username = sibling_node.get("text")
    except Exception:
        pass

    if content_username != username:
        e.click_by_xpath_direct(content, username_xpath)
        sleep(2)
        e.clear_text_adb_keyboard()
        sleep(2)
        e.type_text(username)
        e.click_by_xpath_direct(e.get_lxml_tree(), ok_xpath)

    password_xpath = format_xpath_attrs_exact(text="Password")
    e.click_by_xpath_direct(e.get_lxml_tree(), password_xpath)

    e.clear_text_adb_keyboard()
    sleep(2)

    local_password = password
    if country_iso:
        local_password = local_password.split("_country")[0]
        local_password += "_country-{}".format(country_iso)
    e.type_text(local_password)
    e.click_by_xpath_direct(e.get_lxml_tree(), ok_xpath)
    sleep(1)
    e.scroll_down(distance=200)
    sleep(3)
    content = e.get_lxml_tree()
    if content.xpath(format_xpath_attrs(text="8.8.8.8")):
        # Click on the DNS field
        e.click_by_xpath_direct(content, format_xpath_attrs(text="8.8.8.8"))
        sleep(2)
        e.clear_text_adb_keyboard()
        sleep(2)

        # Get the default gateway to use as DNS
        default_dns = os.getenv("DNS_SERVER", "8.8.8.8")
        e.type_text(default_dns)
        sleep(2)
        content = e.get_lxml_tree()
        e.click_by_xpath_direct(content, format_xpath_attrs(text="OK"))
        sleep(2)
        content = e.get_lxml_tree()

    sleep(2)
    toggle_xpath = format_xpath_attrs("net.typeblog.socks:id/switch_action_button")
    e.click_by_xpath(e.get_lxml_tree(), toggle_xpath)
    sleep(3)
    content = e.get_lxml_tree()
    connection_request = format_xpath_attrs(text="Connection request")
    if content.xpath(connection_request):
        e.click_by_xpath_direct(content, format_xpath_attrs(text="OK"))
        sleep(2)
        content = e.get_lxml_tree()

    while content.xpath(format_xpath_attrs("permission_allow_button")):
        e.click_by_xpath_direct(content, format_xpath_attrs("permission_allow_button"))
        sleep(2)
        content = e.get_lxml_tree()

    checked = content.xpath(toggle_xpath)[0].get("checked")
    if checked == "false":
        e.click_by_xpath_direct(e.get_lxml_tree(), toggle_xpath)
        sleep(7)
    sleep(2)
    assert e.test_connectivity(), "proxy is not working"
    e.click_home_button()
    e.main_logger.info("proxy connected successfully...")


app_setup_functions = {"proxy": proxy_setup}
all_apps = set(app_setup_functions.keys())
failed = {app: [] for app in app_setup_functions}
failed = defaultdict(list)
failed["all"] = []
done = {app: [] for app in app_setup_functions}


def setup_device(device: Emulator):
    device_name = device.device_name
    try:
        device.open_phone()
        if device_name not in list(device_state.keys()):
            device_state[device_name] = {
                "setup apps": [],
                "proxy user": "",
                "status": "init",
                "full name": device.get_device_model(),
            }
    except:
        failed["all"].append(device_name)
        print("\n".join([device.device_name, format_exc()]))
        return
    try:
        device.enable_stay_awake()
    except:
        pass
    device.apply_optimizations()
    sleep(2)
    try:
        device.exec_android_sh_command("shell mkdir -p /storage/emulated/0/backup_folder")
    except Exception as e:
        log(device.main_logger, "ERROR", f"failed to create backup folder: {e}", exc_info=True)
    setup_adb_keyboard(device)
    if INSTALL_WITH_PROXY:
        for _ in range(3):
            try:
                # proxy_setup(device)
                break
            except Exception as e:
                log(
                    device.main_logger,
                    "ERROR",
                    f"proxy setup failed: {e}",
                    screenshot=device.take_screenshot(),
                    ui_dump=device.xml_logger(device.get_ui_hierarchy()),
                    exc_info=True,
                )
        else:
            failed["proxy"].append(device_name)
            print("\n".join([device.device_name, format_exc()]))
            raise Exception("proxy setup failed")

    installed_apps = device.get_third_party_apps()
    new_installed = []
    for app in CHANNELS:
        try:
            if FORCE_INSTALL or not app.check_if_installed(installed_apps):
                log(device.main_logger, "INFO", "installing {}".format(app.get_name()))
                app.install_current_app(device)
                log(
                    device.main_logger,
                    "INFO",
                    "{} app installed successfully".format(app.get_name()),
                )
                installed_apps = device.get_third_party_apps()
                if not app.check_if_installed(installed_apps):
                    raise Exception(
                        "app {}, {} is not installed".format(
                            app.get_name(), app.get_package_name()
                        )
                    )
            else:
                log(
                    device.main_logger,
                    "INFO",
                    "skipping installing app: {}".format(app.get_package_name()),
                )
                device.force_stop_app(app.get_package_name())
            handle_permissions(device, app.get_package_name())
            device_state[device_name]["setup apps"].append(app.get_package_name())
        except:
            failed[app.get_package_name()].append(device)
            log(device.main_logger, "ERROR", f"INSTALLATION FAILED: App '{app.get_name()}' ({app.get_package_name()}) can't be installed on device '{device.device_name}'",
                exc_info=True,
            )
            # os._exit(1)
            # raise
            # log()
    return new_installed


def main():
    parser = argparse.ArgumentParser(
        description="Filter devices and write to a JSON file."
    )
    parser.add_argument(
        "--with-proxy",
        type=str,
        help="install apps and also the proxy app, default is True.",
        default="True",
    )
    parser.add_argument(
        "--force-install",
        type=str,
        help="install apps even it was already installed, default is False.",
        default="False",
    )
    parser.add_argument("--devices", type=str, help="devices to setup.", default="all")
    parser.add_argument(
        "--threaded",
        type=bool,
        help="setup all devices at the same time; might cause some random adb errors",
        default=False,
    )
    args = parser.parse_args()
    global INSTALL_WITH_PROXY, FORCE_INSTALL
    INSTALL_WITH_PROXY = args.with_proxy.lower() == "true"
    FORCE_INSTALL = args.force_install.lower() == "true"
    if args.devices in [None, "all"]:
        devices = get_devices()
    elif args.devices == "new":
        devices = get_new_devices()
    else:
        devices = args.devices.split(",")

    THREADED = args.threaded

    threads = []

    for device in devices:
        e = Emulator(1, device)
        if THREADED:
            thread = threading.Thread(target=setup_device, args=(e,))
            threads.append(thread)
            thread.start()
        else:
            setup_device(e)
    if THREADED:
        for thread in threads:
            thread.join()
    print("proxies", proxy)
    print(failed)
    with open("device-state.json", "w") as f:
        f.write(json.dumps(device_state, indent=4))


if __name__ == "__main__":
    main()
