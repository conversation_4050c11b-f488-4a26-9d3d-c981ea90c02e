from __future__ import annotations

import base64
import json
import logging
import os
import re
import subprocess
import tempfile
import unicodedata
import zipfile
from os import getenv
from pathlib import Path
from time import sleep
from typing import TYPE_CHECKING, Any
from constants import COUNTRY_NAME_MAP
import requests

if TYPE_CHECKING:
    from Emulator import Emulator

SERVER = getenv("BACKEND_SERVER_URL")
MM_BACKEND_KEY = getenv("MM_BACKEND_KEY")

COUNTRIES_LOWER = {country.lower() for country in COUNTRY_NAME_MAP.values()}

class NoBackupFoundException(Exception):
    pass


def find_node_with_attribute(tree, attribute, value):
    """Find a node with the given attribute and value."""
    return tree.xpath(f"//*[@{attribute}='{value}']")[0]


def decode_strings(encoded_strings):
    decoded_strings = [base64.b64decode(s).decode("utf-8") for s in encoded_strings]
    return decoded_strings


def escape_match_string(match_string: str):
    return "".join([char if char not in "{}{}@/" else " " for char in match_string])


def normalize_spaces(input_string):
    return re.sub(r"\s+", " ", input_string)


def remove_characters_and_extra_spaces(input_string: str):
    return normalize_spaces(escape_match_string(input_string.strip()))


def get_stopwords():
    with open("stopwords.json", "r", encoding="utf-8") as f:
        return decode_strings(json.loads(f.read()))


def word_in_stopwords(word: str):
    stopwords = get_stopwords()
    return remove_characters_and_extra_spaces(word.lower()) in stopwords


def validate_word(word: str):
    if word is None:
        return False
    word = word.strip().lower()
    if word_in_stopwords(word) or word in [
        "",
        "null",
        "?",
        "unknown",
        "undefined",
        "0",
        "none",
        "n/a",
        "na",
        "لتعديل أو مسح أو الإبلاغ عن إساءة".strip(),
        "و خدمات أخرى إضغط هنا لزياره موقعنا".strip(),
        "ابحث بكلمتين على الأقل",
        "إبحث بنفس الاسم مرة أخرى",
        "تم تعريفه كمتطفل",
        "بحماية من Truecaller",
        "Load more Results..",
        "Get more Results & Info",
        "_error"
    ] or word in COUNTRIES_LOWER:
        return False
    banned_words = ["truecaller", "realcaller", "true caller", "true_caller", "getcontact", "hellocaller"]
    for banned_word in banned_words:
        if banned_word in word.lower():
            return False
    return True


config_container = []


def load_config() -> dict[str, Any]:
    if len(config_container) == 0:
        with open("config.json", "r", encoding="utf-8") as f:
            CONFIG = json.loads(f.read())
        config_container.append(CONFIG)
        Path(CONFIG["RESULT_PATH"]).mkdir(parents=True, exist_ok=True)

    return config_container[0]


def get_new_devices():
    try:
        with open("devices.json", "r") as f:
            devices = sum(json.loads(f.read()).keys())
    except:
        devices = []
    current_devices = get_devices()
    new_devices = [device for device in current_devices if device not in devices]
    return new_devices


def is_a_physical_device(device_name: str):
    return not device_name.isdigit() and not device_name.startswith("emulator")


def get_devices():
    result = subprocess.run(
        ["adb", "devices"], capture_output=True, text=True, encoding="utf-8"
    )
    devices = [
        device.split("\t")[0]
        for device in result.stdout.splitlines()[1:-1]
        if device.split("\t")[1].strip() == "device"
    ]
    return devices


def type_proxy_config(e: Emulator, tree, placeholder: str, input: str):
    """
    Legacy function for old proxy app.
    """
    e.click_on_node(find_node_with_attribute(tree, "text", placeholder))
    if placeholder not in ["Username", "Password"]:
        e.clear_all_text_compatible(9)
    e.type_text(input)
    e.click_done_button_indirect()


def uninstall_extra_apps(
    e: Emulator,
    excluded_apps={
        "pro.ksanumbers",
        "menodag.arabdevs.android.app",
        "com.cryptoproxy.proxyclient",
        "com.whatsapp",
    },
):
    apps = set(e.get_third_party_apps())
    apps_to_delete = apps - excluded_apps
    print("deleting : ", apps_to_delete)
    for app in apps_to_delete:
        e.uninstall_app(app)


def remove_emojis(text):
    return "".join(
        char for char in text if not unicodedata.category(char).startswith("So")
    )


def process_names_list(names: list):
    names = [remove_emojis(name) for name in names if name and validate_word(name)]
    names_dict = {}
    for name in names:
        if name not in list(names_dict.keys()):
            names_dict[name] = 1
        else:
            names_dict[name] += 1
    return names_dict


def populate_full_device_names(emulator_instances: list[Emulator]):
    try:
        with open("device-state.json", "r") as f:
            device_state = json.loads(f.read())
    except:
        device_state = {}
    for e in emulator_instances:
        try:
            if e.device_name not in list(device_state.keys()):
                device_state[e.device_name] = {
                    "setup apps": [],
                    "proxy user": "",
                    "status": "init",
                    "full name": e.get_device_model(),
                }
        except:
            print(f"failed getting info for {e.device_name}")
    with open("device-state.json", "w") as f:
        device_state = f.write(json.dumps(device_state, indent=4))


def get_hits_from_names(names: list):
    names_dict = {}
    for name in names:
        if name not in names_dict:
            names_dict[name] = 1
        else:
            names_dict[name] += 1
    return names_dict


def count_available_devices(devices: list[Emulator]) -> int:
    return sum([not x.running for x in devices])


def format_xpath_attrs(
    resource_id: str | None = None, text: str | None = None, **kwargs
) -> str:
    dc = kwargs.copy()
    if resource_id:
        dc["resource-id"] = resource_id
    if text:
        dc["text"] = text

    xpath_query = (
        "//node["
        + " and ".join(
            [
                f"contains(@{attr.replace('_', '-')}, {repr(value)})"
                for attr, value in dc.items()
            ]
        )
        + "]"
    )
    return xpath_query


def format_xpath_attrs_exact(
    resource_id: str | None = None, text: str | None = None, **kwargs
) -> str:
    dc = kwargs.copy()
    if resource_id:
        dc["resource-id"] = resource_id
    if text:
        dc["text"] = text

    xpath_query = (
        "//node["
        + " and ".join(
            [f"@{attr.replace('_', '-')}={repr(value)}" for attr, value in dc.items()]
        )
        + "]"
    )
    return xpath_query


def prune_phone_number(phone_number: str) -> str:
    return re.sub(r"[\(\)\-\s\+]", "", phone_number)


def proxy_change_country(e: Emulator, country_iso: str):
    e.open_app("net.typeblog.socks")
    content = e.get_lxml_tree()
    e.scroll_down(450)
    password_xpath = format_xpath_attrs_exact(text="Password")
    e.click_by_xpath_direct(e.get_lxml_tree(), password_xpath)
    e.double_click_focused_node()
    # e.clear_all_text_compatible(1, True)
    # e.clear_all_text_compatible(32)
    local_password = os.getenv("PROXY_PASSWORD", "")
    if country_iso:
        local_password = local_password.split("_country")[0]
        local_password += "_country-{}".format(country_iso)
        input("press enter")
    e.type_text(local_password)
    ok_xpath = format_xpath_attrs(text="OK")
    e.click_by_xpath_direct(e.get_lxml_tree(), ok_xpath)

    toggle_xpath = format_xpath_attrs("switch_action_button")
    e.click_by_xpath_direct(e.get_lxml_tree(), toggle_xpath)
    sleep(3)
    content = e.get_lxml_tree()
    checked = content.xpath(toggle_xpath)[0].get("checked")
    if checked == "false":
        e.click_by_xpath_direct(e.get_lxml_tree(), toggle_xpath)
        sleep(3)
    assert e.test_connectivity(), "proxy is not working"
    e.click_home_button()
    e.main_logger.info("proxy connected successfully...")


logger = logging.getLogger("backup_utils")
logger.handlers.clear()
logger.addHandler(logging.StreamHandler())


def process_and_upload_backup(file_data: bytes, metadata: dict):
    """
    Processes backup data for upload to a specified endpoint.

    Args:
        file_data (bytes): Raw bytes of a compressed archive
        metadata (dict): Metadata about the backup

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if not file_data:
            logger.error("No file data provided for processing")
            return False

        package_name = metadata.get("package_name", "unknown")
        logger.info(f"Processing backup for {package_name}")

        endpoint_url = f"{SERVER}/api/phone-tracking"
        logger.info(f"Uploading backup to endpoint: {endpoint_url}")

        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as tmp_file:
            tmp_file.write(file_data)
            tmp_file_path = tmp_file.name

        try:
            with open(tmp_file_path, "rb") as f:
                response = requests.post(
                    endpoint_url,
                    files={"file": f},
                    headers={"Authorization": MM_BACKEND_KEY},
                    data={"number": metadata["number"]},
                    timeout=600,
                )

                if response.status_code in (200, 201):
                    logger.info(f"Successfully uploaded backup to {endpoint_url}")
                    return True

                logger.error(
                    f"Failed to upload backup: {response.status_code} - {response.text}"
                )
                return False

        except Exception as e:
            logger.error(f"Exception during upload: {str(e)}")
            return False

        finally:
            # Clean up temporary file
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)

    except Exception as e:
        logger.error(f"Error in process_and_upload_backup: {str(e)}")
        return False


def download_and_process_backup(
    emulator: Emulator,
    package_name: str,
    path: str = "/storage/emulated/0/backup_folder",
):
    """
    Downloads a backup file from the server and prepares it for restoration.

    Args:
        emulator (Emulator): The emulator instance to use
        package_name (str): The package name to restore
        path (str): The path on the device where the backup should be placed

    Returns:
        None

    Raises:
        NoBackupFoundException: If no backup is found
        Exception: For other errors during the process
    """
    try:
        logger.info(f"Starting backup download process for package: {package_name}")

        # Get backup info from server
        endpoint_url = f"{SERVER}/api/request-number"
        response = requests.get(endpoint_url, headers={"Authorization": MM_BACKEND_KEY})

        if response.status_code == 404:
            raise NoBackupFoundException

        response.raise_for_status()
        result = response.json()

        if result.get("status") != "success":
            raise Exception(f"Server error: {result.get('status')}")

        number_info = result.get("number_info", {})
        if not number_info or not number_info.get("file_path"):
            raise Exception("No backup file path received")

        # Download backup file
        backup_url = f"{SERVER}{result['file_url']}"
        logger.info(f"Downloading backup from: {backup_url}")
        logger.info(
            f"Backup info: Number={number_info.get('number')}, Status={number_info.get('status')}"
        )

        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as tmp_file:
            response = requests.get(
                backup_url, stream=True, headers={"Authorization": MM_BACKEND_KEY}
            )
            response.raise_for_status()

            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    tmp_file.write(chunk)
            tmp_file_path = tmp_file.name
            logger.info(f"Download completed, saved to: {tmp_file_path}")

        # Extract and push backup
        with tempfile.TemporaryDirectory() as extract_dir:
            folder_package = os.path.join(extract_dir, package_name)
            os.makedirs(folder_package, exist_ok=True)

            with zipfile.ZipFile(tmp_file_path, "r") as zip_ref:
                zip_ref.extractall(folder_package)

            target_path = os.path.join(path, package_name)
            logger.info(f"Pushing backup to device at: {target_path}")
            if not emulator.push_file(folder_package, target_path, override=True):
                raise Exception("Failed to push backup to device")

            logger.info(f"Successfully prepared backup for {package_name}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error: {str(e)}")
        raise Exception(f"Network error: {str(e)}")
    except zipfile.BadZipFile:
        logger.error("Invalid zip file received")
        raise Exception("Invalid backup file")
    except Exception as e:
        logger.error(f"Error processing backup: {str(e)}")
        raise
    finally:
        # Clean up the temporary zip file
        if "tmp_file_path" in locals() and os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)
