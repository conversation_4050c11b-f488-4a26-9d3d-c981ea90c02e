import xml.etree.ElementTree as ET
def does_node_have_attribute(node, attribute_key, attribute_value) :
    assert type(node) == ET.Element
    if attribute_key in node.attrib :
        return node.attrib[attribute_key] == attribute_value
    else : return False
def does_node_have_attributes(node, attribute_pairs : dict) :
    assert type(node) == ET.Element
    for key in attribute_pairs :
        value=attribute_pairs[key]
        if node.attrib.get(key, "nonExistent!") != value : return False
    return True
def find_node_with_attribute(node, attribute_key, attribute_value):
    assert type(node) == ET.Element
    if does_node_have_attribute(node, attribute_key, attribute_value) : return node
    for child in node:
        result = find_node_with_attribute(child, attribute_key, attribute_value)
        if result is not None:
            return result
    return None
def find_node_with_attributes(node, attribute_pairs : dict) :
    assert type(node) == ET.Element
    if does_node_have_attributes(node, attribute_pairs) : return node
    for child in node:
        result = find_node_with_attributes(child, attribute_pairs)
        if result is not None:
            return result
    return None
def find_nodes_with_attributes(node, attribute_pairs : dict) :
    assert type(node) == ET.Element
    nodes = []
    if does_node_have_attributes(node, attribute_pairs) :  nodes.append(node)
    for child in node:
        nodes += find_nodes_with_attributes(child, attribute_pairs)
    return nodes
def find_node_parent_with_attribute(node, attribute_key, attribute_value):
    assert type(node) == ET.Element
    if any([does_node_have_attribute(child, attribute_key, attribute_value) for child in node]) : return node
    for child in node :
        result = find_node_parent_with_attribute(child, attribute_key, attribute_value)
        if result is not None : return result
    return None
def find_node_parent_with_attributes(node, attribute_pairs):
    assert type(node) == ET.Element
    if any([does_node_have_attributes(child, attribute_pairs) for child in node]) : return node
    for child in node :
        result = find_node_parent_with_attributes(child, attribute_pairs)
        if result is not None : return result
    return None
def get_index_of_child_with_attribute_value(node, attribute_key, attribute_value) :
    assert type(node) == ET.Element
    for i in range(len(node)) :
        child = node[i]
        if does_node_have_attribute(child, attribute_key, attribute_value) : return i
    raise Exception("Couldn't find a child with that attribute")
def get_middle_points(bounds_str : str):
    bounds = [int(num) for num in bounds_str.strip('[]').replace('][', ',').split(',')]
    
    x_middle = (bounds[0] + bounds[2]) // 2
    y_middle = (bounds[1] + bounds[3]) // 2
    
    return x_middle, y_middle
def get_attribute_values_of_node(node, attribute_key) :
    assert type(node) == ET.Element
    results = []
    def extract_text(node):
        if attribute_key in node.attrib and node.attrib[attribute_key]:
            results.append(node.attrib[attribute_key].strip())
        for child in node:
            extract_text(child)
    extract_text(node)
    return results
def get_clickable_pixels_of_node(node) :
    assert type(node) == ET.Element
    return get_middle_points(node.attrib["bounds"])
def get_pointer_of_node_with_attr(node, attribute_key, attribute_value, recursed=False) :
    path = []
    # if not recursed : path.append("root")
    assert type(node) == ET.Element
    if does_node_have_attribute(node, attribute_key, attribute_value) : return path
    for i, child in enumerate(node):
        result = get_pointer_of_node_with_attr(child, attribute_key, attribute_value, True)
        if result is not None:
            return [i]+result
    return None
def fetch_node_with_pointer(node, pointer) :
    for point in pointer :
        children = [child for child in node]
        node = children[point]
    return node
def get_node_values(tree, key) :
    r= {}
    if key in tree.attrib :
        r[tree] = tree.attrib[key]
    for child in tree : r.update(get_node_values(child, key))
    return r