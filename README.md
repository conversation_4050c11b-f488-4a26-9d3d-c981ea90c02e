# Android Collector

A tool for collecting and processing phone information across multiple Android devices.

## Overview

Android Collector is a distributed system that manages a pool of Android devices (both physical and emulated) to perform phone number lookup tasks across multiple applications. It provides a framework for automating mobile app interactions to extract contact information.

## Features

- Automated device discovery and management
- Task-based architecture for performing lookups across different applications
- Proxy support for geolocation spoofing
- Request queuing and processing
- Error handling and recovery mechanisms
- Results reporting via API and logging

## Requirements

- Python 3.8+
- Android Debug Bridge (ADB)
- Android devices/emulators
- Network connection for API communication

## Installation

1. Clone the repository:
   ```
   git clone https://git.beinmedia.com/asaadali/android-collector.git
   cd android-collector
   ```

2. Install the required Python dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Configure your environment variables in a `.env` file:
   ```
   BACKEND_IP=your_backend_ip
   BACKEND_PORT=your_backend_port
   MM_BACKEND_KEY=your_api_key
   TG_CHANNEL_ID=your_telegram_channel_id  # For error reporting
   TG_BOT_TOKEN=your_telegram_bot_token    # For error reporting
   PROXY_IP=your_proxy_ip                  # Optional
   PROXY_PORT=your_proxy_port              # Optional
   PROXY_USERNAME=your_proxy_username      # Optional
   PROXY_PASSWORD=your_proxy_password      # Optional
   ```

4. Create a `devices.json` file to configure your devices:
   ```json
   {
     "emulators": ["emulator-5554", "emulator-5556"],
     "pdevices": ["device1_serial", "device2_serial"]
   }
   ```

## Configuration

The system can be configured by modifying:

- `config.json` - Contains system-wide settings
- `devices.json` - Lists available devices
- `.env` - Environment variables for connections

## Usage

### Starting the Collector

Run the entry point script to start the collector system:

```
python entry_point.py
```

Options:
- `--skip-bad-devices` - Skip inactive devices from devices.json
- `--all-devices` - Discover all active devices

### Setting Up Devices

To set up devices separately:

```
python setup_devices.py
```

Options:
- `--with-proxy` - Install proxy app (default: True)
- `--force-install` - Force reinstallation of apps (default: False)
- `--devices` - Specify devices to set up (default: all)
- `--threaded` - Set up all devices simultaneously (default: False)

## Architecture

The system consists of several key components:

- **DeviceManager**: Manages the pool of Android devices
- **RequestManager**: Handles phone lookup requests
- **TaskRunner**: Executes tasks on available devices
- **Task**: Base class for app-specific implementations
- **Emulator**: Abstracts device interaction

## Adding New App Support

To add support for a new app:

1. Create a new class that extends `Task`
2. Implement the required methods
3. Register the app in the `CHANNELS` list in `Emulator.py`

## Troubleshooting

- Check `logs/` directory for detailed logs
- Screenshots and XML dumps are saved in `screenshots/` and `xml_log/` directories
- If devices are not detected, ensure ADB is properly set up
