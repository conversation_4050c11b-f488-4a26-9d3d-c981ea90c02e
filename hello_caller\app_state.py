from Task import AppStateBase


class Hello<PERSON><PERSON>rAppState(AppStateBase):
    CHOOSE_LANGUAGE = "choose_language"
    PRIVACY_POLICY_AGREE = "privacy_policy_agree"
    SPAM_BLOCKING_POPUPS = "spam_blocking_popups"
    ENTER_EMAIL = "enter_email"
    CONTINUE_WITH_GOOGLE = "continue_with_google"
    CHOOSE_GOOGLE_ACCOUNT = "choose_google_account"
    SKIP_FOR_NOW = "skip_for_now"
    CLICK_ON_SEARCH = "click_on_search"
    SEARCH_SCREEN = "search_screen"
    SEARCH_INPUT_SCREEN = "search_input_screen"
    SCROLL_AND_FETCH_NAMES = "scroll_and_fetch_names"
    CANCEL_PAST_COPIES = "Cancel_past_copies"
    NO_RESULTS = "No_results"
    ALL_DAILY_SEARCH_ATTEMPTS_CONSUMED = "all_daily_search_attempts_consumed"
    IDENTIFIED_THE_CALLER = "identified_the_caller"
    NO_RESULTS_FOUND = "no_results_found"
    CHECK_GOOGLE_PLAY_SETTINGS = "check_google_play_settings"

    # ENTER_NAME_AND_PASSWORD = "enter_name_and_password"
    # ONLY_PASSWORD = "only_password"
    UNKNOWN = "unknown"
