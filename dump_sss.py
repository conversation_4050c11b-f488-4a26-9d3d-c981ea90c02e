from utils import get_devices
devices = get_devices()
from Emulator import Emulator
from dotenv import load_dotenv
from os import getenv
load_dotenv()
import requests
import json
from time import sleep, perf_counter
TG_BOT_TOKEN = getenv("TG_BOT_TOKEN")
TELEGRAM_CHANNEL_ID = "-1002183940646"



def send_telegram_photos(chat_id, images, caption="", reply_to_message_id=None):
        files = {}
        media = []
        
        for i, path in enumerate(images) :
            with open(path, "br") as f : image=f.read()
            name = f'photo{i}'
            files[name] = image
            media.append(dict(type='photo', media=f'attach://{name}'))
        media[0]['caption'] = caption
        return requests.post(f'https://api.telegram.org/bot{TG_BOT_TOKEN}/sendMediaGroup', data={'chat_id': chat_id, 'media': json.dumps(media), 'reply_to_message_id': reply_to_message_id }, files=files, timeout=15)
def send_telegram_photo(message, image_path) :
    url_photo = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendPhoto"
    with open(image_path, 'rb') as image_file:
        payload_photo = {
            'chat_id': TELEGRAM_CHANNEL_ID,
            'caption': message
        }
        files = {
            'photo': image_file
        }
        response_photo = requests.post(url_photo, data=payload_photo, files=files, timeout=15)
    return response_photo.json()

paths= []
names = []
counter=perf_counter()
for i, device in enumerate(devices) :
    e=Emulator(1, device)
    try :
        path=e.take_screenshot()
        paths.append(path)
        names.append(f"{e.device_name} - {e.model}")
        print("got ss for "+e.device_name)
        # sleep(5-(perf_counter() - counter))
        # send_telegram_photo(f"{e.device_name} - {e.model}", path)
        # counter = perf_counter()
        if (i+1)%5 == 0 :
            time_to_sleep = 15-(perf_counter() - counter)
            sleep(time_to_sleep)
            send_telegram_photos(TELEGRAM_CHANNEL_ID, paths, "\n".join(names))
            counter=perf_counter()
            # print(paths, names)
            paths, names = [], []
    except : print("failed getting ss for "+e.device_name)
time_to_sleep = 15-(perf_counter() - counter)
sleep(time_to_sleep if time_to_sleep>=0 else 0)
send_telegram_photos(TELEGRAM_CHANNEL_ID, paths, "\n".join(names))