import argparse
import json
import os
import time
from datetime import datetime
from queue import Empty, Queue
from threading import Lock, Thread
from traceback import format_exc
from typing import Any, Callable, Dict, List, Optional, Type

import requests
from dotenv import load_dotenv

from docker_restarter import (DOCKER_RESTARTING,
                              restart_docker_containers_every_six_hours)
from Emulator import ALL_CHANNELS_WITHOUT_EXCLUDE, CH<PERSON><PERSON><PERSON>, Emulator
from error import log, setup_logger
from schemas import PhoneRequest, RequestStatusEnum, SearchTypeEnum
from setup_devices import setup_device
from Task import RateLimitException, Task
from utils import get_devices, load_config
from VirtualMasterLauncher import VirtualMasterLauncher

load_dotenv(".env")

CONFIG = load_config()
MM_BACKEND_KEY = os.getenv("MM_BACKEND_KEY")
SERVER_URL = os.getenv("BACKEND_SERVER_URL", "http://localhost:5000")
DEVICE_SERIAL_ID = 1
TESTING = os.getenv("TESTING", "false").lower() == "true"
BAD_DEVICES_TOLERANCE = os.getenv("BAD_DEVICES_TOLERANCE", "true").lower() == "true"
MASTER_TOLERANCE = os.getenv("MASTER_TOLERANCE", "true").lower() == "true"
GET_ALL_DEVICES = os.getenv("GET_ALL_DEVICES", "true").lower() == "true"
INCLUDE_PHYSICAL = os.getenv("INCLUDE_PHYSICAL", "true").lower() == "true"
INCLUDE_EMULATORS = os.getenv("INCLUDE_EMULATORS", "true").lower() == "true"
SETUP_VM_DEVICES = os.getenv("SETUP_VM_DEVICES", "false").lower() == "true"
CONSECUTIVE_ERRORS_LIMIT = int(os.getenv("CONSECUTIVE_ERRORS_LIMIT", 4))
WAITING_THREADS_COUNTER = 0
SERVER_ID = os.getenv("SERVER_ID")
if not SERVER_ID:
    raise ValueError("SERVER_ID is not set")

device_logger = setup_logger("device_manager", "logs/device_manager.log")
request_logger = setup_logger("request_manager", "logs/request_manager")
run_task_logger = setup_logger("run_task", "logs/run_task")

devices_queue: Queue[Emulator] = Queue()
requests_queue: Queue[PhoneRequest] = Queue()
LOCK = Lock()


def add_device(device: Emulator) -> None:
    """Add a device to the manager and queue."""

    devices_queue.put(device)
    log(device_logger, "INFO", f"Added device: {device}")


def add_when_ready(emulator: Emulator) -> None:
    start_time = time.perf_counter()
    try:
        setup_device(emulator)
    except Exception as e:
        log(
            emulator.main_logger,
            "error",
            f"Error setting up device: {e}",
            send_on_telegram=True,
            exc_info=True,
        )
        if not BAD_DEVICES_TOLERANCE:
            log(
                emulator.main_logger,
                "error",
                "Bad devices tolerance is disabled, exiting...",
            )
            exit(-1)
    log(
        emulator.main_logger,
        "info",
        f"Successful setup, took: {time.perf_counter() - start_time:.2f} seconds",
        send_on_telegram=True,
    )
    emulator.start_device()
    add_device(emulator)


def get_device(timeout: Optional[float] = None) -> Optional[Emulator]:
    """Get an available device, optionally with a timeout"""
    global WAITING_THREADS_COUNTER
    with LOCK:
        WAITING_THREADS_COUNTER += 1
    try:
        device = devices_queue.get(timeout=timeout)
        device.mark_as_used()
        return device
    except Empty:
        log(device_logger, "INFO", "No devices available within timeout period")
    finally:
        with LOCK:
            WAITING_THREADS_COUNTER -= 1


def return_device(device: Emulator) -> None:
    """Return a device to the pool if it's still usable"""
    for _ in range(12):  # wait up to ~60 seconds total
        if device.get_own_state():
            devices_queue.put(device)
            log(device_logger, "DEBUG", f"Returned device to pool: {device}")
            return
        time.sleep(5)

    log(
        device_logger,
        "ERROR",
        f"Device {device} not returned to pool (unusable)",
    )
    repair_thread = Thread(
        target=periodic_repair_device,
        args=(device,),
        daemon=True,
        name=f"RepairThread-{device.device_name}",
    )
    repair_thread.start()


def populate_active_devices() -> None:
    with open("devices.json", "r", encoding="utf-8") as f:
        devices_config = json.load(f)
    devices_to_use: Dict[str, str] = {}

    for device in devices_config.get("pdevices", []):
        if (
            device in devices_config.get("force_exceclude", [])
            or device in devices_config.get("VMs", [])
            or device in devices_config.get("master_devices", [])
        ):
            continue

        devices_to_use[device] = "physical"
        is_remote_device = (
            ":" in device and device.split(":")[0].replace(".", "").isdigit()
        )
        if is_remote_device:
            log(device_logger, "INFO", f"Connecting to remote device {device}...")
            try:
                os.system(f"adb connect {device}")
            except Exception:
                pass

    active_devices = get_devices()
    not_working_devices: List[str] = []
    working_devices: List[str] = []

    if GET_ALL_DEVICES:
        for device in active_devices:
            if device not in devices_to_use.keys():
                # Check if device is in force_exclude list first
                if (
                    device in devices_config.get("force_exceclude", [])
                    or device in devices_config.get("VMs", [])
                    or device in devices_config.get("master_devices", [])
                ):
                    continue

                # Check if device is an emulator (starts with "localhost:")
                is_emulator = device.startswith(
                    "localhost:"
                )  # or device.startswith("emulator")

                # Only include device if the corresponding flag is enabled
                if is_emulator and not INCLUDE_EMULATORS:
                    continue
                if not is_emulator and not INCLUDE_PHYSICAL:
                    continue

                devices_to_use[device] = "emulator" if is_emulator else "physical"

    for device in devices_to_use.keys():
        if device not in active_devices:
            if BAD_DEVICES_TOLERANCE:
                log(
                    device_logger,
                    "error",
                    f"Couldn't find an active device named {device}. \n Continue...",
                )
                not_working_devices.append(device)
                continue

            log(
                device_logger,
                "error",
                f"Couldn't find an active device named {device}. \nRemove it from devices"
                " list or change BAD_DEVICES_TOLERANCE in .env file. Exiting...",
            )
            os._exit(1)
        working_devices.append(device)

    if not_working_devices:
        log(
            device_logger,
            "info",
            f"Skipping not working devices: {', '.join(not_working_devices)}",
        )
    devices_config["working_devices"] = working_devices
    devices_config["not_working_devices"] = not_working_devices
    devices_config["force_exceclude"] = devices_config.get("force_exceclude", [])
    with open("devices.json", "w", encoding="utf-8") as f:
        json.dump(devices_config, f, indent=4, ensure_ascii=False)
    init_and_push_devices(working_devices)


def setup_vm_devices() -> None:
    with open("devices.json", "r", encoding="utf-8") as f:
        devices_config = json.load(f)

    cnt = 1
    vms = devices_config.get("VMs", [])
    master_devices = devices_config.get("master_devices", [])

    if bool(master_devices) != bool(vms):
        log(device_logger, "ERROR", "No master devices found in devices.json")
        os._exit(1)

    active_devices = get_devices()

    for master in master_devices:
        if master not in active_devices:
            if MASTER_TOLERANCE:
                log(
                    device_logger,
                    "ERROR",
                    f"Master device {master} not found in active devices, Continue...",
                )
                continue

            log(
                device_logger,
                "ERROR",
                f"Master device {master} not found in active devices",
            )
            os._exit(1)

        is_remote_master = (
            ":" in master and master.split(":")[0].replace(".", "").isdigit()
        )
        if is_remote_master:
            log(device_logger, "INFO", f"Connecting to remote master {master}...")
            try:
                os.system(f"adb connect {master}")
            except Exception:
                pass

        e = Emulator(f"Master:{cnt}", master)
        setup_device(e)
        vm = VirtualMasterLauncher(e, master)
        Thread(target=vm.run, daemon=True, name=f"VM:{cnt}").start()
        cnt += 1
    time.sleep(120)  # wait for all VMs to start

    init_and_push_vms(vms)


def init_and_push_vms(devices: List[str]) -> None:
    global DEVICE_SERIAL_ID
    for device in devices:
        current_serial = DEVICE_SERIAL_ID
        DEVICE_SERIAL_ID += 1

        emulator = Emulator(current_serial, device)
        try:
            emulator.exec_android_sh_command(f"adb connect {device}")
        except Exception as e:
            pass
        log(
            device_logger,
            "INFO",
            "Starting setup and initializing...",
            device=emulator.name_and_model,
        )
        Thread(target=add_when_ready, args=(emulator,)).start()


def init_and_push_devices(devices: List[str]) -> None:
    global DEVICE_SERIAL_ID

    cnt = 0
    threads = []
    for device in devices:
        current_serial = DEVICE_SERIAL_ID
        DEVICE_SERIAL_ID += 1

        emulator = Emulator(current_serial, device)
        log(
            device_logger,
            "INFO",
            "Starting setup and initializing...",
            device=emulator.name_and_model,
        )
        setup_thread = Thread(target=add_when_ready, args=(emulator,))
        setup_thread.start()
        threads.append(setup_thread)

    # for thread in threads:
    #     try:
    #         thread.join()
    #         cnt += 1
    #     except Exception as exc:
    #         log(device_logger, "ERROR", f"Error discovering device {device}: {exc}")

    channels = ", ".join([app.get_name() for app in CHANNELS])
    # log(
    #     device_logger,
    #     "info",
    #     f"Started {cnt} devices, collecting tasks: {channels}",
    # )


def fetch_requests() -> List[PhoneRequest]:
    url = SERVER_URL + "/api/requests"

    if TESTING:
        return fetch_requests_testing()

    if WAITING_THREADS_COUNTER > 100:
        return []
    num_request = requests.get(
        url,
        params={"limit": 5, "server_id": SERVER_ID},
        headers={"Authorization": MM_BACKEND_KEY},
    )
    reqs = num_request.json()["data"]

    requests_list = []
    for req in reqs:
        search_type = SearchTypeEnum.ByNumber
        if req.get("search_type") == "by_name":
            search_type = SearchTypeEnum.ByName

        # Parse status
        status = RequestStatusEnum.NEW
        if req.get("status"):
            try:
                status = RequestStatusEnum(req["status"])
            except ValueError:
                log(
                    request_logger,
                    "WARNING",
                    f"Unknown status value: {req['status']}, defaulting to NEW",
                )

        requests_list.append(
            PhoneRequest(
                id=req["id"],
                search_type=search_type,
                country_code=req["country_code"],
                query=req["query"],
                status=status,
                is_paid_user=bool(req["is_paid_user"]),
                created_at=datetime.strptime(
                    req["created_at"], "%a, %d %b %Y %H:%M:%S GMT"
                ),
                modified_at=datetime.strptime(
                    req["modified_at"], "%a, %d %b %Y %H:%M:%S GMT"
                ),
                retries=req.get("retries", 0),
                apps=req["apps"][1:-1].split(",") if req["apps"] else [],
            )
        )
    return requests_list


def fetch_requests_testing() -> List[PhoneRequest]:
    global TESTING
    if TESTING == "STOP":
        return []
    queries = [
        "22055070",
        "52527216",
        "1832255",
        "99119311",
        "69088885",
        "67757762",
        "22970301",
        "61667530",
        "67770210",
        "96656625",
    ]
    reqs = [
        PhoneRequest(
            id=i,
            search_type=SearchTypeEnum.ByNumber,
            country_code="965",
            query=x,
            status=RequestStatusEnum.NEW,
            is_paid_user=False,
            created_at=datetime.now(),
            modified_at=datetime.now(),
        )
        for i, x in enumerate(queries)
    ]
    TESTING = "STOP"
    return reqs


def populate_requests() -> int:
    """Fetch new requests and add them to the queue. Return count of new requests."""
    try:
        requests = fetch_requests()
        count = 0

        for request in requests:
            requests_queue.put(request)
            count += 1
        if count > 0:
            log(request_logger, "INFO", f"Fetched {count} new requests")
        return count
    except Exception as exc:
        log(request_logger, "ERROR", f"Error fetching requests: {exc}")
        return 0


def get_request(timeout: Optional[float] = None) -> Optional[PhoneRequest]:
    try:
        return requests_queue.get(timeout=timeout)
    except Empty:
        return None


def save_completed_request(
    request: PhoneRequest, results: List[Dict[str, Any]]
) -> None:
    apps, local_results = [], {}
    all_success, all_failed = True, False

    for res in results:
        if res.get("success") is True:
            all_failed = False
            app = res.get("channel")
            apps.append(app)
            local_results[app] = list(res.get("results").keys())
        else:
            all_success = False
    all_channels_no_exclude = get_all_tasks_for_request(request)
    if all_success and len(apps) == len(all_channels_no_exclude):
        status = RequestStatusEnum.SUCCESS
    elif all_failed:
        status = RequestStatusEnum.FAILED
    else:
        status = RequestStatusEnum.UNCOMPLETED
    # Data for API request
    data = {
        "id": request.id,
        "result": local_results,
        "apps": apps,
        "status": status.value,
    }

    # Complete data for Telegram with full results
    full_data = {"api_request_payload": data, "complete_results": results}

    url = SERVER_URL + "/api/query/update"

    # Attempt to send data with retries

    try:
        if not TESTING:
            res = requests.post(
                url,
                json=data,
                headers={"Authorization": MM_BACKEND_KEY},
                timeout=30,
            )
            res.raise_for_status()
        else:
            if not os.path.exists("results"):
                os.makedirs("results")
            full_data["raw_results"] = results
            with open(f"results/{request.id}_{request.query}.json", "w") as f:
                json.dump(full_data, f, indent=4, ensure_ascii=False)
        log(
            request_logger,
            "INFO",
            f"Request {request.id}, saved: {len(data['result'])} results, apps: {len(data['apps'])}, status: {status}",
        )
        return

    except Exception as e:
        error_msg = format_exc()
        log(
            request_logger,
            "WARNING",
            f"Error saving results for request {request.id}: {error_msg}",
        )

    # If all attempts failed, send the data to Telegram as a JSON file

    try:
        filename = (
            f"request_{request.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        log(
            request_logger,
            "ERROR",
            f"Failed to save results to server for request {request.id}",
            file=full_data,
            filename=filename,
            phone=request.query,
            country_code=request.country_code,
            apps=apps,
            exc_info=True,
        )
    except Exception as e:
        pass


def choose_tasks_for_request(request: PhoneRequest) -> tuple[List[Type[Task]], bool]:
    """Determine which task channels can handle the given request."""
    channels = [channel for channel in CHANNELS if channel.can_handle_request(request)]
    filtered = list(filter(lambda x: x.get_name() not in request.apps, channels))
    st1 = {x.get_name() for x in channels}
    st2 = {x.get_name() for x in filtered}
    st1.difference_update(st2)
    is_excluded = False
    if len(st1) > len(st2):
        log(
            run_task_logger, "INFO", f"request {request.id}, excluded channels: {(st1)}"
        )
        is_excluded = True
    return list(filtered), is_excluded


def get_all_tasks_for_request(request: PhoneRequest) -> List[Type[Task]]:
    """Get all tasks that can handle the given request without excluding any channels."""
    channels = [
        channel
        for channel in ALL_CHANNELS_WITHOUT_EXCLUDE
        if channel.can_handle_request(request)
    ]
    return channels


#############


class ThreadWithReturnValue(Thread):
    """Thread subclass that can return a value from the target function."""

    def __init__(
        self, group=None, target=None, name=None, args=(), kwargs=None, *, daemon=None
    ):
        """Initialize the thread with the same parameters as Thread."""
        if kwargs is None:
            kwargs = {}
        self._return = None
        self._exception = None

        def function_wrapper(*args, **kwargs):
            try:
                if target:
                    self._return = target(*args, **kwargs)
            except Exception as e:
                self._exception = e

        super().__init__(
            group=group,
            target=function_wrapper,
            name=name,
            args=args,
            kwargs=kwargs,
            daemon=daemon,
        )


def periodic_repair_device(device: Emulator) -> None:
    """
    Repair a device periodically to ensure it's always working.
    """
    while True:
        try:
            if repair_device(device):
                return
        except Exception as e:
            pass
        time.sleep(300)


def repair_device(device: Emulator) -> None:
    """
    Repair a corrupted device by attempting reconnection or rebooting.

    Args:
        device: The Emulator device that needs repair
    """

    device_name = f"{device.device_name}_{device.device_id}"

    log(
        device_logger,
        "INFO",
        f"Starting repair process for device {device_name}",
    )

    # Check if device is in active devices list first

    active_devices = get_devices()
    # check if it's an ip
    is_remote_device = (
        ":" in device_name and device_name.split(":")[0].replace(".", "").isdigit()
    )

    if device_name in active_devices:
        log(device_logger, "INFO", f"Device {device_name} is in active devices list")

        # TODO: it would be better to reboot also the remote device
        # but we should make sure we are able to reconnect and
        # the ip, port will not change, or try to search for it...
        if not is_remote_device:
            log(device_logger, "INFO", f"Rebooting device {device_name}...")
            device.exec_android_sh_command("reboot")
            time.sleep(60)

        else:
            log(
                device_logger,
                "INFO",
                f"Attempting to repair remote device {device_name}",
            )

            # Try reconnection with increasing backoff between attempts

            # Force disconnect first to clear any stale connections
            device.exec_android_sh_command(f"adb disconnect {device_name}")
            time.sleep(3)

            # Attempt to connect to the device
            connect_result = device.exec_android_sh_command(
                f"adb connect {device_name}"
            )
            log(
                device_logger,
                "INFO",
                f"Connection result: {connect_result}",
            )
            # Check if device is now available

            time.sleep(3)

        setup_device(device)
        # Verify the device is working
        if device.get_own_state():
            add_device(device)
            device.start_device()
            try:
                log(
                    device_logger,
                    "INFO",
                    f"Successfully repaired device {device_name} with soft recovery",
                    send_on_telegram=True,
                )
            finally:
                return True


def run_task(task_cls: Type[Task], request: PhoneRequest) -> Dict[str, Any]:
    consecutive_errors = 0
    while consecutive_errors < CONSECUTIVE_ERRORS_LIMIT:
        device: Optional[Emulator] = None
        sleeping, is_corrupted = 0, False
        try:
            # Get a device, waiting up to one hour
            log(
                run_task_logger,
                "DEBUG",
                f"Getting device for channel {task_cls.get_name()}",
            )
            device = get_device(timeout=14400)
            log(
                run_task_logger,
                "DEBUG",
                f"Device {device} found for channel {task_cls.get_name()}",
            )
            if not device:
                log(
                    run_task_logger,
                    "DEBUG",
                    f"No device available for channel {task_cls.get_name()}, request_id {request.id}",
                )
                sleeping = 10
                continue
            if not device.get_own_state():
                log(
                    run_task_logger,
                    "ERROR",
                    f"Device {device} not working, it will not be returned to pool",
                )
                is_corrupted = True
                continue

            if task_cls.requires_root() and not device.is_device_rooted():
                log(
                    run_task_logger,
                    "DEBUG",
                    f"Device {device} is not rooted, returning to pool, request_id {request.id}",
                    send_on_telegram=False,
                )

                sleeping = 10
                continue

            if task_cls.requires_non_root() and device.is_device_rooted():
                log(
                    run_task_logger,
                    "DEBUG",
                    f"Device {device} is rooted but task requires non-rooted device, returning to pool, request_id {request.id}",
                    send_on_telegram=False,
                )
                sleeping = 10
                continue

            log(
                run_task_logger,
                "DEBUG",
                f"Running task {task_cls.get_name()} for request {request.id} on device {device}",
            )

            task = task_cls(emulator=device, request=request)

            # Create a future to run the task
            result = task.run_state_based()

            log(
                run_task_logger,
                "INFO",
                f"Task {task_cls.get_name()} completed for request {request.id}, "
                f"emulator: {device.device_name}, "
                f"fetched results: {len(result)}",
            )
            return {
                "success": True,
                "channel": task_cls.get_name(),
                "results": result,
            }
        except RateLimitException:
            log(
                run_task_logger,
                "INFO",
                f"Limit reached for {task_cls.get_name()}, request: {request.id}, returning to pool",
            )
            return {
                "success": False,
                "channel": task_cls.get_name(),
                "results": {},
                "error": "Rate limit reached",
            }
        except Exception as exc:
            consecutive_errors += 1
            log(
                run_task_logger,
                "ERROR",
                f"Error in channel {task_cls.get_name()} for request {request.id}: {exc}",
                exc_info=True,
                screenshot=device.take_screenshot(),
                xml_log=device.xml_logger(device.get_ui_hierarchy()),
                device=device.full_name_writable,
            )
        finally:
            if is_corrupted:
                # Initiate a thread to repair the device
                repair_thread = Thread(
                    target=periodic_repair_device,
                    args=(device,),
                    daemon=True,
                    name=f"RepairThread-{device.device_name}",
                )
                repair_thread.start()
                log(
                    run_task_logger,
                    "INFO",
                    f"Started repair thread for device {device.device_name}",
                )
            if device is not None and not is_corrupted:
                return_device(device)
                log(
                    run_task_logger,
                    "INFO",
                    f"Device {device} returned to pool",
                )
            if sleeping > 0:
                time.sleep(sleeping)
    return {
        "success": False,
        "channel": task_cls.get_name(),
        "results": {},
        "error": f"Task failed with {CONSECUTIVE_ERRORS_LIMIT} consecutive errors",
    }


def process_request(request: PhoneRequest) -> None:
    """Process a request by running all matching tasks concurrently."""
    log(run_task_logger, "DEBUG", f"Processing request {request.id}: {request.query}")
    tasks, is_excluded = choose_tasks_for_request(request)

    if not tasks and not is_excluded:
        log(
            run_task_logger,
            "DEBUG",
            f"No task handlers available for request {request.id}",
        )

    futures = [
        ThreadWithReturnValue(target=run_task, args=(task_cls, request))
        for task_cls in tasks
    ]
    for future in futures:
        future.start()
    results = []
    for i, future in enumerate(futures):
        try:
            future.join()
            result = future._return
            assert isinstance(result, dict), f"Result {result} is not a dict"
            results.append(result)

        except Exception as exc:
            log(
                run_task_logger,
                "ERROR",
                f"Error getting task result: {exc}",
                traceback=True,
            )
            results.append(
                {
                    "success": False,
                    "channel": tasks[i].get_name(),
                    "results": {},
                    "error": str(exc),
                }
            )

    save_completed_request(request, results)


def run() -> None:
    """Main task processing loop that pulls requests from the queue and processes them.

    This method continuously monitors the request queue, submitting tasks for processing
    when requests are available, and handles any exceptions that occur.
    """
    log(run_task_logger, "INFO", "Starting TaskRunner main processing loop")

    while True:
        try:
            while DOCKER_RESTARTING:
                time.sleep(5)

            request = get_request(timeout=10)

            if request:
                log(
                    run_task_logger,
                    "DEBUG",
                    f"Retrieved request ID {request.id} for processing",
                )
                # Submit the request processing to the executor
                Thread(target=process_request, args=(request,)).start()
                log(
                    run_task_logger,
                    "DEBUG",
                    f"Submitted request ID {request.id} to executor",
                )
        except Empty:
            pass
        except Exception as exc:
            log(
                run_task_logger,
                "ERROR",
                f"Error in task runner main loop: {exc}",
                traceback=True,
            )

            time.sleep(1)


poller_logger = setup_logger("request_poller", "logs/request_poller")


def request_poller(interval: int = 10) -> None:
    """Background thread to poll for new requests."""
    url = SERVER_URL + "/api/request-reset"
    if not TESTING:
        requests.put(
            url,
            params={"server_id": SERVER_ID},
            headers={"Authorization": MM_BACKEND_KEY},
        )
    while True:
        try:
            populate_requests()
        except Exception as exc:
            poller_logger.exception(f"Error polling for requests: {exc}")
        time.sleep(interval)


def main() -> None:
    main_logger = setup_logger("main", "logs/main.log")
    log(main_logger, "info", "Started script")
    main_logger.info(f"Loaded configuration: {CONFIG}")

    log(main_logger, "INFO", "Populating active devices")
    populate_active_devices()
    log(main_logger, "INFO", "Populating active devices done")

    threads = [
        Thread(
            target=request_poller,
            args=(CONFIG["request_poll_interval"],),
            daemon=True,
            name="RequestPoller",
        ),
        Thread(target=run, daemon=True, name="TaskRunner"),
    ]

    if SETUP_VM_DEVICES:
        threads.append(Thread(target=setup_vm_devices, daemon=True, name="VMSetup"))

    docker_thread = Thread(
        target=restart_docker_containers_every_six_hours,
        daemon=True,
        name="DockerRestarter",
    )
    threads.append(docker_thread)

    for thread in threads:
        thread.start()
        main_logger.info(f"Started thread: {thread.name}")
        time.sleep(1)

    try:
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        main_logger.info("Shutting down gracefully...")

    except Exception as exc:
        main_logger.exception(f"Unexpected error: {exc}")

    finally:
        main_logger.info("Shutdown complete")


if __name__ == "__main__":
    main()
