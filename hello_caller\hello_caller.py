from __future__ import annotations

from datetime import datetime, timedelta
import time
from typing import TYPE_CHECKING

import xml_tree_cursor
from schemas import CountryCode<PERSON>num, PhoneRequest, SearchTypeEnum, ScrapingStatusEnum
from utils import remove_emojis, validate_word
from hello_caller.app_state import Hello<PERSON><PERSON><PERSON><PERSON><PERSON>State
from hello_caller.state_handler import <PERSON><PERSON><PERSON><PERSON>
from Task import FailedAutomationException, Task, NotFoundException, NotClickableException, RateLimitException
from error import setup_logger

from hello_caller.config import DEBUGGING, SEARCH_ITEM_RESOURCE_ID, handle_google_accounts, set_google_accounts, NUMBER_OF_NAME_REQUESTS

USERNAME, EMAIL, PASSWORD = handle_google_accounts()
LIMIT_REACHED_WAITING = datetime.now()
NAME_LIMIT_REACHED_WAITING = datetime.now()


if TYPE_CHECKING:
    from Emulator import Emulator

class HelloCaller(Task):
    
    AppState = HelloCallerAppState
    XPATH_PATTERNS = {
        AppState.CHOOSE_LANGUAGE: [
            "//node[contains(@text, 'ENGLISH')]",
        ],
        AppState.PRIVACY_POLICY_AGREE: [
            "//node[contains(@resource-id, 'com.callerid.wie:id/chAgree')]",
            "//node[contains(@resource-id, 'com.callerid.wie:id/lnAgreeTerms')]",
        ],
        AppState.SPAM_BLOCKING_POPUPS: ["//node[contains(@text, 'Identify unknown numbers, spam or business numbers! Know the real identity and location of callers from all around the world.')]"],
        AppState.ENTER_EMAIL: [
            "//node[contains(@resource-id, 'com.callerid.wie:id/etEmail')]",
        ],
        AppState.CONTINUE_WITH_GOOGLE: [
            "//node[contains(@resource-id, 'com.callerid.wie:id/btnGoogle')]",
        ],
        AppState.CHOOSE_GOOGLE_ACCOUNT: [
            f"//node[contains(@text, '{EMAIL}')]",
        ],
        AppState.SKIP_FOR_NOW: ["//node[contains(@text, 'Skip for now')]"],
        AppState.CLICK_ON_SEARCH: [
            "//node[contains(@resource-id, 'com.callerid.wie:id/btnNotifications')]",
            "//node[contains(@text, 'Recently searches')]",
        ],
        AppState.SEARCH_INPUT_SCREEN: [
            "//node[contains(@text, 'Search for phone number')]",
            "//node[contains(@resource-id, 'com.callerid.wie:id/tvPhoneNumber')]",
        ],
        AppState.SCROLL_AND_FETCH_NAMES: [ 
            "//node[contains(@resource-id, 'com.callerid.wie:id/tvName')]",
        ],
        AppState.CANCEL_PAST_COPIES:[
            "//node[contains(@text, 'Do you want to search for')]",
        ],
        AppState.NO_RESULTS:[
            "//node[contains(@resource-id, 'com.callerid.wie:id/tvDoYouKnowTheNumber')]",
        ],
        AppState.ALL_DAILY_SEARCH_ATTEMPTS_CONSUMED:[
           "//node[contains(@text, 'You have consumed all your daily search attempts or your points')]",
        ],
        AppState.IDENTIFIED_THE_CALLER: [
            "//node[contains(@text, 'Hello? Caller ID just identified the caller for you. Is that worth a 5 stars rating?')]",
        ],
        AppState.NO_RESULTS_FOUND: [
            "//node[contains(@text, 'No results found')]",
        ],
        AppState.CHECK_GOOGLE_PLAY_SETTINGS: [
            "//node[contains(@text, 'Check that Google Play is enabled on your device and that you')]",
        ],
        # AppState.ENTER_NAME_AND_PASSWORD: [
        #     "//node[contains(@resource-id, 'com.callerid.wie:id/tvName')]",
        # ],
        # AppState.ONLY_PASSWORD: [
        #     "//node[contains(@resource-id, 'com.callerid.wie:id/btnLogin')]",
        # ],
    }

    def __init__(
        self,
        emulator: Emulator,
        request: PhoneRequest,
        clear_data_on_first_start: bool = True,
    ) -> None:
        self.logger = setup_logger(
            f"HelloCaller_{emulator.device_name}",
            f"logs/HelloCaller_{emulator.device_name}.log",
        )
        try:
            super().__init__(
                emulator,
                request,
                clear_data_on_first_start,
            )
            self.request = request
            self.status = None
            self.state_handler = StateHandler(self.emulator, self.logger, request, self.set_done_automation)
            self.state_handlers = {
                self.AppState.CHOOSE_LANGUAGE: self.state_handler.choose_language,
                self.AppState.PRIVACY_POLICY_AGREE: self.state_handler.handle_privacy_policy,
                self.AppState.SPAM_BLOCKING_POPUPS: self.state_handler.press_skip,
                self.AppState.ENTER_EMAIL: self.state_handler.enter_email,
                self.AppState.CONTINUE_WITH_GOOGLE: self.state_handler.continue_with_google,
                self.AppState.CHOOSE_GOOGLE_ACCOUNT: self.state_handler.choose_google_account,
                self.AppState.SKIP_FOR_NOW: self.state_handler.handle_skip,
                self.AppState.CLICK_ON_SEARCH: self.state_handler.click_on_search,
                self.AppState.SEARCH_INPUT_SCREEN: self.search_input_screen,
                # self.AppState.SCROLL_AND_FETCH_NAMES: self.get_names,
                self.AppState.CANCEL_PAST_COPIES: self.state_handler.handle_copies,
                self.AppState.NO_RESULTS: self.no_names_found,
                self.AppState.ALL_DAILY_SEARCH_ATTEMPTS_CONSUMED: self.handel_reach_limit,
                self.AppState.IDENTIFIED_THE_CALLER: self.state_handler.handle_identified_the_caller,
                self.AppState.NO_RESULTS_FOUND: self.no_names_found,
                self.AppState.CHECK_GOOGLE_PLAY_SETTINGS: self.cleared_app_data_init,
                # self.AppState.ENTER_NAME_AND_PASSWORD: self.state_handler.enter_name_and_password,
                # self.AppState.ONLY_PASSWORD: self.state_handler.enter_password,
                self.AppState.UNKNOWN: self.handle_unknown_state,
            }
        except Exception as e:
            log(self.logger, "error", f"Error initializing HelloCaller: {str(e)}", True)
            raise

    
    
    def change_current_region(self, new_region) -> bool:
        try:
            return super().change_current_region(new_region)
        except Exception as e:
            log(self.logger, "error", f"Error changing region: {str(e)}", True)
            return False

    def app_open_step(self, populate_nodes: bool = True):
        try:
            return self.run_automation()
        except Exception as e:
            log(self.logger, "error", f"Error in app_open_step: {str(e)}", True)
            return False

    def search_input_screen(self):
        print("search_input_screen_from_hello_caller")
        try:
            res = self.state_handler.search_input_screen()
            if not res:
                return res
            if self.request.search_type == SearchTypeEnum.ByNumber:
                return self.get_names()

            time.sleep(10)
            content = self.emulator.get_lxml_tree()
            names_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/tvName')]"
            names_elements = content.xpath(names_xpath)
            if not names_elements:
                self.results = {}
                self.done_automation = True
                time.sleep(2)
                self.emulator.click_back_button()
                time.sleep(2)
                if DEBUGGING:
                    log(self.logger, "debug", "No names found in search results", True)
                return True


            if DEBUGGING:
                log(self.logger, "debug", "Names found in search results", True)
            
            time.sleep(2)
            self.results = self.scroll_and_fetch_numbers(self.request.query)
            time.sleep(2)
            self.done_automation = True
            self.emulator.click_back_button()
            return True
        except Exception as e:
            log(self.logger, "error", f"Error in get_numbers: {str(e)}", True)
            return False

    
    def handel_reach_limit(self):
        try:
            print("handel_reach_limit")
            global USERNAME, EMAIL, PASSWORD, LIMIT_REACHED_WAITING, NAME_LIMIT_REACHED_WAITING
            USERNAME, EMAIL, PASSWORD = handle_google_accounts()
            if USERNAME and EMAIL and PASSWORD:
                self.state_handler.change_google_accounts()
                log(self.logger, "info", "Google accounts changed", True, self.emulator.take_screenshot())
            else:
                set_google_accounts()
                USERNAME, EMAIL, PASSWORD = handle_google_accounts()
                self.status = ScrapingStatusEnum.LIMIT_REACHED                    
                self.results = {}
                self.done_automation = True
                self.emulator.click_back_button()
                time.sleep(3)
                self.emulator.click_back_button()

                if self.request.search_type == SearchTypeEnum.ByName:
                    NAME_LIMIT_REACHED_WAITING = datetime.now() + timedelta(hours=1)
                    log(self.logger, "warning", "Name limit reached, waiting for 1 hour", True, self.emulator.take_screenshot())
                else:
                    LIMIT_REACHED_WAITING = datetime.now() + timedelta(hours=1)
                    log(self.logger, "warning", "Number limit reached, waiting for 1 hour", True, self.emulator.take_screenshot())

            return True
        # from DeviceIDChanger import DeviceIDChanger
        #     device_id_changer = DeviceIDChanger(
        #         emulator=self.emulator,
        #         target_package="com.callerid.wie",
        #         target_apk="com.callerid.wie.apk",
        #     )
        #     device_id_changer.run()
        #     self.emulator.open_phone()
        #     self.cleared_app_data_init()
        #     self.app_open_step()
        except Exception as e:
            log(self.logger, "error", f"Error in change_google_accounts: {str(e)}", True)
            return False
        
    
    def cleared_app_data_init(self):
        try:
            self.emulator.clear_data(self.package_name)
            self.emulator.open_app(self.package_name)
            time.sleep(10)
        except Exception as e:
            log(self.logger, "error", f"Error in cleared_app_data_init: {str(e)}", True)
            raise

    def no_names_found(self) -> bool:
        try:
            print("No_names")
            time.sleep(5)
            self.results = {}
            self.done_automation = True
            self.emulator.click_back_button()
            time.sleep(3)
            self.emulator.click_back_button()
            return True
        except Exception as e:
            log(self.logger, "error", f"Error in no_names_found: {str(e)}", True)
            return False

    def get_names(self) -> bool:
        try:
            print("get_names")
            time.sleep(5)
            self.results = self.scroll_and_fetch_names(self.request.query)
            self.done_automation = True
            self.emulator.click_back_button()
            time.sleep(3)
            self.emulator.click_back_button()

            return True
        except Exception as e:
            log(self.logger, "error", f"Error in get_names: {str(e)}", True)
            return False

    def set_done_automation(self):
        self.results = {}
        self.done_automation = True
    
    def scroll_and_fetch_numbers(self, name: str) -> dict[str, int]:
        try:
            print("scroll_and_fetch_numbers")
            all_results_of_number: dict[str, int] = {}
            ui_dump = self.emulator.get_ui_hierarchy()
            node_tree = self.emulator.get_node_tree(ui_dump)
            text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                node_tree, {"resource-id": 'com.callerid.wie:id/tvPhoneNumber'}
            )
            if not text_nodes_array:
                time.sleep(20)
                ui_dump = self.emulator.get_ui_hierarchy()
                node_tree = self.emulator.get_node_tree(ui_dump)
                text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                    node_tree,
                    {"resource-id": 'com.callerid.wie:id/tvPhoneNumber'},
                )
                if not text_nodes_array:
                    if DEBUGGING:
                        log(
                            self.emulator.main_logger,
                            "error",
                            f"Timeout while waiting for app to load names, skipping number {name}",
                            True,
                            self.emulator.take_screenshot(),
                            ui_dump=self.emulator.xml_logger(ui_dump),
                        )
                    return all_results_of_number
            if DEBUGGING:
                log(self.logger, "debug", "getting results")
            ui_dump = self.emulator.get_ui_hierarchy()
            node_tree = self.emulator.get_node_tree(ui_dump)
            count = 0
            
            self.true_loop_start_time = time.perf_counter()
            while True:
                text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                    node_tree, {"resource-id": 'com.callerid.wie:id/tvPhoneNumber'}
                )
                count += 1
                
                for node in text_nodes_array:
                    number = node.attrib["text"]
                    
                    all_results_of_number[number] = all_results_of_number.get(number, 0) + 1
                self.emulator.scroll_down(400)
                time.sleep(5)
                new_ui_dump = self.emulator.get_ui_hierarchy()
                if new_ui_dump == ui_dump or len(all_results_of_number) > NUMBER_OF_NAME_REQUESTS:
                    break
                ui_dump = new_ui_dump
                node_tree = self.emulator.get_node_tree(ui_dump)
                if time.perf_counter() - self.true_loop_start_time > 2700:
                    raise Exception("Timed out while getting numbers")
            return all_results_of_number
        except NotFoundException as e:
            log(self.logger, "error", f"UI elements not found while fetching numbers: {str(e)}", True)
            return {}
        except NotClickableException as e:
            log(self.logger, "error", f"UI elements not clickable while fetching numbers: {str(e)}", True)
            return {}
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in scroll_and_fetch_numbers: {str(e)}", True)
            return {}
    
    def scroll_and_fetch_names(self, phone: str) -> dict[str, int]:
        try:
            print("scroll_and_fetch_names")
            all_results_of_number: dict[str, int] = {}
            ui_dump = self.emulator.get_ui_hierarchy()
            node_tree = self.emulator.get_node_tree(ui_dump)
            text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                node_tree, {"resource-id": SEARCH_ITEM_RESOURCE_ID}
            )
            if not text_nodes_array:
                time.sleep(20)
                ui_dump = self.emulator.get_ui_hierarchy()
                node_tree = self.emulator.get_node_tree(ui_dump)
                text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                    node_tree,
                    {"resource-id": SEARCH_ITEM_RESOURCE_ID},
                )
                if not text_nodes_array:
                    if DEBUGGING:
                        log(
                            self.emulator.main_logger,
                            "error",
                            f"Timeout while waiting for app to load names, skipping number {phone}",
                            True,
                            self.emulator.take_screenshot(),
                            ui_dump=self.emulator.xml_logger(ui_dump),
                        )
                    return all_results_of_number
            if DEBUGGING:
                log(self.logger, "debug", "getting results")
            ui_dump = self.emulator.get_ui_hierarchy()
            node_tree = self.emulator.get_node_tree(ui_dump)
            count = 0
            
            self.true_loop_start_time = time.perf_counter()
            while True:
                text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                    node_tree, {"resource-id": SEARCH_ITEM_RESOURCE_ID}
                )
                count += 1
                
                for node in text_nodes_array:
                    name = remove_emojis(node.attrib["text"])
                    if not validate_word(name):
                        continue
                    all_results_of_number[name] = all_results_of_number.get(name, 0) + 1
                self.emulator.scroll_down(400)
                time.sleep(1)
                try:
                    more_button_xpath = "//node[contains(@resource-id, 'com.callerid.wie:id/consShowAllNames')]"
                    content = self.emulator.get_lxml_tree()
                    try:
                        more_button_bounds = self.emulator.get_bounds_by_xpath(content, more_button_xpath)
                        if more_button_bounds:
                            if DEBUGGING:
                                log(self.logger, "debug", "more button found", False)
                            self.emulator.click_element_by_bounds(more_button_bounds)
                            time.sleep(1)
                    except: pass
                except Exception as e:
                    log(self.logger, "error", f"Error clicking more button: {str(e)}", True)
                new_ui_dump = self.emulator.get_ui_hierarchy()
                if new_ui_dump == ui_dump:
                    break
                ui_dump = new_ui_dump
                node_tree = self.emulator.get_node_tree(ui_dump)
                if time.perf_counter() - self.true_loop_start_time > 2700:
                    raise Exception("Timed out while getting names")
            return all_results_of_number
        except NotFoundException as e:
            log(self.logger, "error", f"UI elements not found while fetching names: {str(e)}", True)
            return {}
        except NotClickableException as e:
            log(self.logger, "error", f"UI elements not clickable while fetching names: {str(e)}", True)
            return {}
        except Exception as e:
            log(self.logger, "error", f"Unexpected error in scroll_and_fetch_names: {str(e)}", True)
            return {}

    def run_state_based(self):
        
        if self.request.search_type == SearchTypeEnum.ByName and NAME_LIMIT_REACHED_WAITING > datetime.now():
            raise RateLimitException
        elif self.request.search_type == SearchTypeEnum.ByNumber and LIMIT_REACHED_WAITING > datetime.now():
            raise RateLimitException
        
        self.emulator.open_app(self.get_package_name())
        try:
            self.run_automation()
        finally:
            try:
                self.emulator.force_stop_app(self.get_package_name())
                self.emulator.empty_ram_with_adb()
            except Exception:
                pass
        if not self.done_automation:
            raise FailedAutomationException
        return self.results

    @staticmethod
    def get_name() -> str:
        return "hello_caller"

    @staticmethod
    def get_package_name() -> str:
        return "com.callerid.wie"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        try:
            name = cls.get_package_name()
            return name in installed_apps
        except Exception as e:
            log(cls.logger, "error", f"Error checking if app is installed: {str(e)}", True)
            return False

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        return [CountryCodeEnum.ALL]

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        return [SearchTypeEnum.ByNumber, SearchTypeEnum.ByName]
    
    @classmethod
    def requires_root(cls) -> bool:
        """Override to specify that this task requires root access."""
        return False
