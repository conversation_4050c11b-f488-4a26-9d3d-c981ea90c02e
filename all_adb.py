from utils import *
from Emulator import *
import argparse, threading
parser = argparse.ArgumentParser()
parser.add_argument("--c", type=str)
parser.add_argument("--threaded", type=bool, help="setup all devices at the same time; might cause some random adb errors")

args = parser.parse_args()
THREADED = args.threaded
command = args.c
es = [Emulator(1, device) for device in get_devices()]
threads = []
for e in es :
    def a() :
        result = e.exec_android_sh_command(command, read_output=True, timeout=9999)
        print(result)
    if THREADED :
        thread = threading.Thread(target=a)
        threads.append(thread)
        thread.start()
    else : a()
for thread in threads :
    thread.join()
    