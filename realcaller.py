from __future__ import annotations

import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import TYPE_CHECKING

import xml_tree_cursor
from error import log, setup_logger
from schemas import CountryCodeEnum, PhoneRequest, RequestStatusEnum, ScrapingStatusEnum, SearchTypeEnum
from Task import AppStateBase, RateLimitException, Task, UnknownAppState 
from utils import load_config, remove_emojis, validate_word
from constants import COUNTRY_NAME_MAP

if TYPE_CHECKING:
    from Emulator import Emulator


CONFIG = load_config()
DEBUGGING = False

SEARCH_ITEM_RESOURCE_ID = "com.realcaller.android:id/search_item_title"
LIMIT_REACHED_WAITING = datetime.now()
WAITING_TIME_IN_HOURS = 1


class RealCallerAppState(AppStateBase):
    INTRO_SCREEN = "intro_screen"
    SEARCH_INPUT_SCREEN = "search_input_screen"
    AGREEMENT_SCREEN = "agreement_screen"
    UNKNOWN = "unknown"
    Caller_ID_SCREEN = "caller_id_screen"
    RATE_APP_SCREEN = "rate_app_screen"
    SEARCH_BY_NAME = "search_by_name"

class RealCaller(Task):
    AppState = RealCallerAppState
    XPATH_PATTERNS = {
        AppState.INTRO_SCREEN: ["//node[contains(@content-desc, 'Real Caller Public')]"],
        AppState.AGREEMENT_SCREEN: [
            "//node[contains(@resource-id, 'menwho.phone.callerid.social:id/') and @content-desc='Radio button to agree to terms and services']",
            "//node[@text='GET STARTED']",
            "//node[contains(@resource-id, 'menwho.phone.callerid.social:id/') and @content-desc='Button to enter the app']"
        ],
        AppState.SEARCH_INPUT_SCREEN: [
            "//node[@class='android.widget.EditText' and contains(@text, 'Type number')]",
            "//node[@text='Search' and @resource-id='menwho.phone.callerid.social:id/']"
        ],
        AppState.Caller_ID_SCREEN: [
            "//node[contains(@resource-id, 'android:id/button2')]"
        ],
        AppState.RATE_APP_SCREEN: [
            "//node[contains(@text, 'Please take a moment to rate the app')]",
            "//node[@text='LATER']",
            "//node[contains(@text, 'rate') and contains(@text, 'app')]"
        ]
    }

    def __init__(self, emulator: Emulator, request: PhoneRequest) -> None:
        self.logger = setup_logger(
            f"RealCaller_{emulator.device_name}",
            f"logs/RealCaller_{emulator.device_name}.log",
        )
        super().__init__(emulator, request)
        
        # Initialize results container
        self.results = {}
        self.done_automation = False
        
        self.state_handlers = {
            self.AppState.INTRO_SCREEN: self.simple_state_handler,
            self.AppState.AGREEMENT_SCREEN: self.handle_agreement_screen,
            self.AppState.Caller_ID_SCREEN: self.handle_caller_id_screen,
            self.AppState.SEARCH_INPUT_SCREEN: self.handle_search_screen,
            self.AppState.RATE_APP_SCREEN: self.handle_rate_app_screen,
            self.AppState.UNKNOWN: self.handle_unknown_state,
        }
        
        # Open the app when initialized
        self.logger.info("Initializing RealCaller and opening app")
        self.emulator.open_app(self.get_package_name())
        time.sleep(5)
   
   

    def handle_agreement_screen(self) -> bool:
        content = self.emulator.get_lxml_tree()
        
        # Click the radio button using tab and enter
        self.emulator.click_tab()
        self.emulator.click_tab()
        self.emulator.click_tab()
        self.emulator.click_tab()
        self.emulator.click_tab()
        self.emulator.click_tab()
        self.emulator.click_tab()
        self.emulator.click_enter()
        time.sleep(2)
        
        # Finally click the Get Started button
        start_xpath = "//node[@text='GET STARTED']"
        start_bounds = self.emulator.get_bounds_by_xpath(content, start_xpath)
        if start_bounds:
            self.emulator.click_element_by_bounds(start_bounds)
            time.sleep(5)
            return True
            
        return False

    def handle_caller_id_screen(self) -> bool:
        content = self.emulator.get_lxml_tree()
        start_xpath = "//node[contains(@resource-id, 'android:id/button2')]"
        start_bounds = self.emulator.get_bounds_by_xpath(content, start_xpath)
        if start_bounds:
            self.emulator.click_element_by_bounds(start_bounds)
            time.sleep(5)
            return True
        return False
    
    def check_current_search_mode(self) -> str:
        content = self.emulator.get_lxml_tree()
        
        # Method 1: Check input field placeholder text (works when field is empty)
        name_text_indicators = [
            "//node[@class='android.widget.EditText' and contains(@text, 'Type name')]",
            "//node[@class='android.widget.EditText' and contains(@text, 'name')]"
        ]
        
        number_text_indicators = [
            "//node[@class='android.widget.EditText' and contains(@text, 'Type number')]",
            "//node[@class='android.widget.EditText' and contains(@text, 'number')]"
        ]
        
        # Check placeholder text first
        for xpath in name_text_indicators:
            if content.xpath(xpath):
                self.logger.debug("Detected name search mode via placeholder text")
                return "name"
                
        for xpath in number_text_indicators:
            if content.xpath(xpath):
                self.logger.debug("Detected number search mode via placeholder text")
                return "number"
        
        # Phone numbers typically end with digits, names typically end with letters
        try:
            edit_text = content.xpath("//node[@class='android.widget.EditText']")
            if edit_text and 'text' in edit_text[0].attrib:
                text = edit_text[0].get('text', '')
                if text and len(text) > 0:
                    # Check last character
                    if text[-1].isdigit():
                        self.logger.debug("Detected number search mode based on numeric input")
                        return "number"
                    elif text[-1].isalpha():
                        self.logger.debug("Detected name search mode based on alphabetic input")
                        return "name"
        except Exception:
            pass
        
        # Default to number if we can't determine
        self.logger.info("Could not determine search mode, assuming number search")
        return "number"

    def handle_search_screen(self) -> bool:
        try:
            self.logger.info("Handling search screen")
            
            # Fresh UI dump to ensure we're working with current state
            content = self.emulator.get_lxml_tree()

            # Get country code from request
            country_code = self.reqeust.country_code

            # Check if the country is already selected before trying to select it
            country_name = COUNTRY_NAME_MAP.get(country_code, "Unknown")
            country_indicator_xpath = f"//node[@text='{country_name}' and @class='android.widget.TextView']"
            alt_country_xpath = f"//node[contains(@text, '{country_name}')]"

            country_already_selected = False
            country_indicators = content.xpath(country_indicator_xpath)
            if not country_indicators:
                country_indicators = content.xpath(alt_country_xpath)

            if country_indicators:
                self.logger.debug(f"{country_name} is already selected, skipping country selection")
                country_already_selected = True

            if not country_already_selected:
                country_selected = self.select_country_by_code(country_code)
                if not country_selected:
                    self.logger.warning(f"Could not select {country_name} as country, will try Kuwait as fallback")
                    # Try Kuwait as fallback
                    if country_code != "965":
                        kuwait_selected = self.select_country_by_code("965")
                        if kuwait_selected:
                            self.logger.debug("Successfully selected Kuwait as fallback country")
                        else:
                            self.logger.debug("Failed to select Kuwait as fallback, proceeding with current country")
                else:
                    self.logger.debug(f"{country_name} selected successfully")
                    time.sleep(2)  # Wait for UI to update after country selection
                    # Get updated UI after country selection
                    content = self.emulator.get_lxml_tree()

            # Check current search mode
            current_mode = self.check_current_search_mode()
            requested_mode = "name" if self.reqeust.search_type == SearchTypeEnum.ByName else "number"
            
            # Toggle search mode if needed
            if current_mode != requested_mode:
                self.logger.info(f"Current search mode: {current_mode}, requested mode: {requested_mode}")
                # Use the new toggle_search_mode method directly with target mode
                toggle_success = self.toggle_search_mode(target_mode=requested_mode)
                
                if toggle_success:
                    self.logger.debug(f"Successfully toggled to {requested_mode} search mode")
                else:
                    self.logger.debug(f"Failed to toggle to {requested_mode} search mode, will try to proceed anyway")
                
                # Short delay after toggling
                time.sleep(2)
            else:
                self.logger.debug(f"Already in {requested_mode} search mode, no need to toggle")

            # Get fresh content after possible country selection and search mode toggle
            content = self.emulator.get_lxml_tree()

            # Try to find the input field for either number or name search
            type_input_x_path = "//node[@class='android.widget.EditText']"
            alt_type_input_x_path = "//node[contains(@resource-id, 'menwho.phone.callerid.social:id/') and @class='android.widget.EditText']"

            # Define search button XPaths here so they're available throughout the method
            search_button_x_path = "//node[@text='Search' and @class='android.widget.Button']"
            alt_search_button_x_path = "//node[contains(@text, 'Search') and @class='android.widget.Button']"

            try:
                type_input_bounds = self.emulator.get_bounds_by_xpath(content, type_input_x_path)
                if not type_input_bounds:
                    type_input_bounds = self.emulator.get_bounds_by_xpath(content, alt_type_input_x_path)
                
                if type_input_bounds:
                    self.emulator.click_element_by_bounds(type_input_bounds)
                    time.sleep(1)
                    
                    # Use clear_all_text to clear the field before typing
                    try:
                        # As a fallback, also use multiple backspace to ensure the field is empty
                        for _ in range(20):  # Use more backspaces to ensure we clear longer numbers
                            self.emulator.press_backspace()
                        time.sleep(0.5)
                    except Exception as e:
                        self.logger.debug(f"Failed to clear text: {str(e)}")
                        
                        # Final attempt: click on the field again and try to clear using basic method
                        self.emulator.click_element_by_bounds(type_input_bounds)
                        time.sleep(0.5)
                        for _ in range(20):
                            self.emulator.press_backspace()
                        time.sleep(0.5)
                    
                    # Enter search query (could be phone number or name)
                    search_type = "name" if self.reqeust.search_type == SearchTypeEnum.ByName else "number"
                    self.logger.debug(f"Entering {search_type}: {self.reqeust.query}")
                    self.emulator.type_text_adb_keyboard(self.reqeust.query)
                    time.sleep(1)


                    # Get updated UI content after typing
                    content = self.emulator.get_lxml_tree()

                    search_button_bounds = self.emulator.get_bounds_by_xpath(content, search_button_x_path)

                    if not search_button_bounds:
                        self.logger.debug("Primary search button XPath failed, trying alternative")
                        search_button_bounds = self.emulator.get_bounds_by_xpath(content, alt_search_button_x_path)

                    if search_button_bounds:
                        # Save the pre-search UI state to check if it changes after search
                        pre_search_content = content
                        
                        # Click the search button
                        self.emulator.click_element_by_bounds(search_button_bounds)
                         # Wait briefly for any toast message to appear
                        time.sleep(0.5)
                        daily_limit_reached = False
                        # Looking for a partial match for the maximum daily message
                        limit_phrases = [
                            "Maximum daily", 
                            "daily limit", 
                            "search has been reached",
                            "Maximum"
                        ]
                        
                        for phrase in limit_phrases:
                            if self.emulator.capture_and_find_text(phrase):
                                self.logger.info(f"Daily limit reached - detected via OCR with phrase: '{phrase}'")
                                global LIMIT_REACHED_WAITING
                                LIMIT_REACHED_WAITING = datetime.now() + timedelta(hours=1)
                                self.status = ScrapingStatusEnum.LIMIT_REACHED
                                daily_limit_reached = True
                                self.results = {}  # Empty results
                                self.done_automation = True
                                
                                raise RateLimitException()   
                        time.sleep(7) 
                        
                        # Get the post-search UI state
                        post_search_content = self.emulator.get_lxml_tree()
                        
                        # Check if we're still in the search interface by looking for the search button
                        post_search_button = post_search_content.xpath(search_button_x_path)
                        if not post_search_button:
                            post_search_button = post_search_content.xpath(alt_search_button_x_path)
                        
                        # If the search button is still visible, we're likely still on the search screen
                        # This indicates no results were found (the toast notification appeared)
                        if post_search_button:
                            self.logger.info(f"No match found for the {search_type}")
                            
                            # Check if input field is still there with our search query
                            input_field_after_search = post_search_content.xpath(type_input_x_path)
                            if not input_field_after_search:
                                input_field_after_search = post_search_content.xpath(alt_type_input_x_path)
                            
                            if input_field_after_search:
                                self.logger.debug("Input field still visible with search query - confirming no results")
                                self.results = {}  # Empty results
                                self.done_automation = True
                                return True
                            
                        # If we got here, we've moved to a different screen - likely results page
                        # Continue with normal results collection flow
                        self.logger.debug("Interface changed after search - likely have results, continuing with normal flow")

            except Exception as e:
                self.logger.warning(f"EditText interaction failed: {str(e)}")
                return False

            # If we got here, we're either in the results screen or need to collect names
            search_type = "name" if self.reqeust.search_type == SearchTypeEnum.ByName else "number"
            self.logger.info(f"Collecting results for {search_type} search")

            try:
                self.results = self.scroll_and_fetch_results(self.reqeust.query)
                
                # Determine what we collected based on search type
                result_type = "phone numbers" if self.reqeust.search_type == SearchTypeEnum.ByName else "names"
                
                if self.results:
                    self.logger.info(f"Found {len(self.results)} {result_type} for {search_type} query {self.reqeust.query}")
                else:
                    self.logger.info(f"No {result_type} found for {search_type} query {self.reqeust.query}")
                
                self.done_automation = True

                # Go back to previous screen
                self.emulator.click_back_button()
                time.sleep(3)  # Wait for UI to update after going back
                
                return True

            except Exception as e:
                self.logger.error(f"Error collecting results: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"Error in handle_search_screen: {str(e)}")
            return False

    def handle_rate_app_screen(self) -> bool:
        """
        Handle the rate app screen that appears asking to rate the app
        We want to click "LATER" to dismiss it
        """
        try:
            self.logger.debug("Rate app dialog detected, looking for Later button")
            content = self.emulator.get_lxml_tree()
            
            # Try to find the LATER button
            later_button_xpaths = [
                "//node[@text='LATER']",
                "//node[contains(@text, 'LATER')]",
                "//node[contains(@text, 'Later')]",
                "//node[contains(@resource-id, 'button') and contains(@text, 'later')]"
            ]
            
            for xpath in later_button_xpaths:
                try:
                    later_button = self.emulator.get_bounds_by_xpath(content, xpath)
                    if later_button:
                        self.logger.debug(f"Found Later button with xpath: {xpath}, clicking it")
                        self.emulator.click_element_by_bounds(later_button)
                        time.sleep(2)
                        return True
                except Exception as e:
                    self.logger.debug(f"Xpath {xpath} failed to find Later button: {str(e)}")
            
            # If we couldn't find the LATER button with specific XPaths, try clicking on coordinates
            # Often the LATER button is on the left side of the dialog
            self.logger.debug("Could not find Later button with xpath, trying to click at typical coordinates")
            # Try clicking at the bottom left of the screen (typical location for "Later" or "Cancel" buttons)
            self.emulator.click_on_coordinates(300, 1200)
            time.sleep(2)
            
            # Check if the dialog is gone
            new_content = self.emulator.get_lxml_tree()
            rate_app_still_visible = False
            for xpath in self.XPATH_PATTERNS[self.AppState.RATE_APP_SCREEN]:
                if new_content.xpath(xpath):
                    rate_app_still_visible = True
                    break
            
            if not rate_app_still_visible:
                self.logger.debug("Rate app dialog dismissed successfully")
                return True
            else:
                self.logger.debug("Rate app dialog still visible after clicking, trying back button")
                self.emulator.click_back_button()
                time.sleep(2)
                return True
            
        except Exception as e:
            self.logger.error(f"Error handling rate app screen: {str(e)}")
            # Try back button as a last resort
            self.emulator.click_back_button()
            time.sleep(2)
            return False

    def handle_unknown_state(self) -> bool:
        self.emulator.click_back_button()
        time.sleep(3)
        return True

    def run_automation(self, max_iterations: int = 30):
        self.logger.info("Starting automation")
        
        for i in range(max_iterations):
            self.logger.info(f"Automation iteration {i+1}/{max_iterations}")
            
            # Get current UI
            content = self.emulator.get_lxml_tree()
            
            # Detect state
            current_state = self.detect_state(content)
            self.logger.info(f"Current state: {current_state}")
            
            # Set the current state (required by simple_state_handler)
            self.current_state = current_state
            
            # Run handler for current state
            if current_state in self.state_handlers:
                handler = self.state_handlers[current_state]
                success = handler()
                
                # If we're done, stop iteration
                if self.done_automation:
                    self.logger.info("Automation completed successfully")
                    return True
                    
                if not success:
                    self.logger.warning(f"Handler for state {current_state} returned failure")
            else:
                self.logger.warning(f"No handler for state: {current_state}")
                
            # Wait between iterations
            time.sleep(3)
            
        self.logger.warning(f"Automation did not complete within {max_iterations} iterations")
        return False

    def scroll_and_fetch_results(self, query: str) -> dict[str, int]:
        ui_dump = self.emulator.get_ui_hierarchy()
        node_tree = self.emulator.get_node_tree(ui_dump)
        
        # Determine search type
        search_type = self.reqeust.search_type
        is_name_search = search_type == SearchTypeEnum.ByName
        
        # Set up the appropriate resource IDs and attributes to look for based on search type
        if is_name_search:
            self.logger.debug("Collecting phone numbers from name search results")
            # When searching by name, we want to find phone numbers in the results
            result_attrs = {
                "resource-id": "menwho.phone.callerid.social:id/",
                "class": "android.widget.TextView"
            }
        else:
            self.logger.debug("Collecting contact names from number search results")
            # When searching by number, we want to find names in the results
            result_attrs = {
                "resource-id": "menwho.phone.callerid.social:id/",
                "class": "android.widget.TextView"
            }
        
        # Try to find all result nodes based on attributes
        result_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
            node_tree, result_attrs
        )
        
        if not result_nodes_array:
            self.logger.debug("No result nodes found with primary query, trying alternative approach")
            # Wait a bit longer and try again
            time.sleep(20)
            ui_dump = self.emulator.get_ui_hierarchy()
            node_tree = self.emulator.get_node_tree(ui_dump)
            result_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                node_tree, result_attrs
            )
            
            if not result_nodes_array:
                log(
                    self.emulator.main_logger,
                    "error", 
                    f"Timeout while waiting for app to load results, skipping query {query}",
                    True,
                    self.emulator.take_screenshot(),
                    ui_dump=self.emulator.xml_logger(ui_dump),
                )
                raise UnknownAppState()

        log(self.emulator.main_logger, "debug", "Getting results")
        all_results: dict[str, int] = {}
        self.true_loop_start_time = time.perf_counter()

        # Keep track of what we've seen to detect end of scrolling
        last_seen_results = set()
        consecutive_same_results = 0
        max_consecutive_same = 3  # Stop after seeing the same results 3 times

        while True:
            # Extract results from the current view
            result_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                node_tree, result_attrs
            )
            
            # Extract the text from each node and add to results
            current_results = set()
            
            for node in result_nodes_array:
                if "text" not in node.attrib:
                    continue
                    
                result_text = remove_emojis(node.attrib["text"])
                
                if is_name_search:
                    # For name search, we want phone numbers
                    # Look for patterns like "+123456789" or numbers with spaces/dashes
                    import re
                    if re.match(r'^\+?\d[\d\s\-]{4,}$', result_text) or result_text.startswith("+"):
                        current_results.add(result_text)
                        if not validate_word(result_text):
                            continue
                        all_results[result_text] = all_results.get(result_text, 0) + 1
                else:
                    # For number search, we want names (skip phone numbers)
                    if result_text.startswith("+") or result_text.isdigit() or "Saudi Arabia" in result_text:
                        continue
                    
                    current_results.add(result_text)
                    if not validate_word(result_text):
                        continue
                    all_results[result_text] = all_results.get(result_text, 0) + 1
            
            # Check if we've reached the end of the list
            if current_results == last_seen_results:
                consecutive_same_results += 1
                if consecutive_same_results >= max_consecutive_same:
                    self.logger.info("End of results detected, same results seen multiple times")
                    break
            else:
                consecutive_same_results = 0
                last_seen_results = current_results

            # Scroll down to see more results
            self.emulator.scroll_down(800)
            time.sleep(1)
            
            # Get updated UI
            new_ui_dump = self.emulator.get_ui_hierarchy()
            if new_ui_dump == ui_dump:
                self.logger.info("End of results detected, UI didn't change after scrolling")
                break
                
            ui_dump = new_ui_dump
            node_tree = self.emulator.get_node_tree(ui_dump)
            
            # Safety timeout
            if time.perf_counter() - self.true_loop_start_time > 2700:
                self.logger.warning("Timed out while getting results, returning partial results")
                break
                
        return all_results
        
    # Alias the old method name to the new method for backward compatibility
    scroll_and_fetch_names = scroll_and_fetch_results

    @staticmethod
    def get_name() -> str:
        return "realcaller"

    @staticmethod
    def get_package_name() -> str:
        return "menwho.phone.callerid.social"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        return [CountryCodeEnum.ALL]

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        return [SearchTypeEnum.ByNumber, SearchTypeEnum.ByName]

    @classmethod
    def requires_non_root(cls) -> bool:
        return True

    def enable_required_permissions(self):
        self.logger.debug("Enabling required permissions via ADB")
        package_name = self.get_package_name()
        
        # Get the list of permissions that the app has requested but not been granted
        requested_permissions = self.emulator.get_requested_permissions(package_name)
        
        if requested_permissions:
            self.logger.info(f"Found {len(requested_permissions)} permissions to grant")
            for permission in requested_permissions:
                try:
                    self.emulator.enable_permission(package_name, permission)
                    self.logger.debug(f"Enabled permission: {permission}")
                except Exception as e:
                    self.logger.debug(f"Failed to enable permission {permission}: {str(e)}")
        else:
            self.logger.debug("No permissions need to be granted")

    def select_country_by_code(self, country_code: str) -> bool:
        # Special handling for 'all' country code
        if country_code == "all":
            self.logger.debug("Country code is 'all', using current country selection")
            return True
        
        if country_code not in COUNTRY_NAME_MAP:
            self.logger.debug(f"Country code {country_code} not found in COUNTRY_NAME_MAP")
            return False
            
        country_name = COUNTRY_NAME_MAP.get(country_code)
        self.logger.debug(f"Attempting to select country: {country_name} (code: {country_code})")
        
        # Get the current screen content
        content = self.emulator.get_lxml_tree()
        
        # Check if country is already selected
        country_indicator_xpath = f"//node[@text='{country_name}' and @class='android.widget.TextView']"
        alt_country_xpath = f"//node[contains(@text, '{country_name}')]"
        
        country_indicators = content.xpath(country_indicator_xpath)
        
        if not country_indicators:
            # Try alternative xpath
            country_indicators = content.xpath(alt_country_xpath)
        
        if country_indicators:
            self.logger.debug(f"{country_name} is already selected, no need to change country")
            return True
        
        self.logger.debug(f"{country_name} not selected, proceeding to select country")
        
        # Define multiple methods to find the country button
        country_button = None
        
        # Method 1: Using specific XPath patterns
        country_button_xpaths = [
            "//node[@NAF='true' and @class='android.widget.Button' and @resource-id='menwho.phone.callerid.social:id/' and @index='1']",
            "//node[@class='android.widget.Button' and @bounds='[942,518][1052,628]']",
            "//node[@class='android.widget.Button' and contains(@resource-id, 'menwho.phone.callerid.social:id/')]",
            "//node[@class='android.widget.Button']"
        ]
        
        for xpath in country_button_xpaths:
            try:
                
                country_button = self.emulator.get_bounds_by_xpath(content, xpath)
                if country_button:
                    self.logger.debug(f"Found country button with xpath: {xpath}")
                    break
            except Exception as e:
                self.logger.debug(f"Xpath {xpath} failed: {str(e)}")
        
        # Method 2: Using text content
        if not country_button:
            current_country_xpaths = [
                f"//node[contains(@text, '{country_name}')]/parent::node()/following-sibling::node()[@class='android.widget.Button']",
                f"//node[contains(@text, '{country_name}')]/parent::node()/parent::node()/following-sibling::node()[@class='android.widget.Button']",
                f"//node[contains(@text, 'Kuwait')]/parent::node()/following-sibling::node()[@class='android.widget.Button']"
            ]
            
            for xpath in current_country_xpaths:
                try:
                    self.logger.debug(f"Trying to find button near country text: {xpath}")
                    country_button = self.emulator.get_bounds_by_xpath(content, xpath)
                    if country_button:
                        self.logger.debug(f"Found country button near country text with xpath: {xpath}")
                        break
                except Exception as e:
                    self.logger.debug(f"Text-based xpath {xpath} failed: {str(e)}")
        
        # Method 3: If all else fails, use hardcoded coordinates
        if not country_button:
            self.logger.debug("Using hardcoded coordinates for country button")
            # Try clicking near the right side of the screen where country buttons typically are
            self.emulator.click_on_coordinates(1000, 570)  # Middle of the country button area
        else:
            # Click the country button if found
            self.logger.debug("Clicking country button to open selection list")
            self.emulator.click_element_by_bounds(country_button)
            
        time.sleep(3)
        
        # Now we should be on the country selection screen
        self.logger.debug(f"Looking for {country_name} in country list")
        
        # Attempt to find country in the list
        country_found = False
        max_scrolls = 20
        
        # Try to find the search box first to search for the country directly
        new_content = self.emulator.get_lxml_tree()
        search_box_xpath = "//node[@class='android.widget.EditText']"
        search_box = None
        
        try:
            search_box = self.emulator.get_bounds_by_xpath(new_content, search_box_xpath)
            if search_box:
               
                self.emulator.click_element_by_bounds(search_box)
                time.sleep(1)
                self.emulator.type_text_adb_keyboard(country_name)
                time.sleep(2)
                
                # Now look for the country in the filtered list
                filtered_content = self.emulator.get_lxml_tree()
                country_xpath = f"//node[contains(@text, '{country_name}')]"
                country_elements = filtered_content.xpath(country_xpath)
                
                if country_elements:
                    country_element = country_elements[0]
                    country_bounds = country_element.get("bounds")
                    
                    if country_bounds:
                        self.emulator.click_element_by_bounds(country_bounds)
                        country_found = True
                        time.sleep(2)
                        return True
        except Exception as e:
            self.logger.debug(f"Search box approach failed: {str(e)}, falling back to scrolling")
        
        # If search approach failed, fall back to scrolling
        for i in range(max_scrolls):
            self.logger.debug(f"Scroll attempt {i+1} to find {country_name}")
            new_content = self.emulator.get_lxml_tree()
            
            # Try multiple xpath patterns to find the country
            country_xpaths = [
                f"//node[@resource-id='menwho.phone.callerid.social:id/' and contains(@text, '{country_name}')]",
                f"//node[contains(@text, '{country_name}')]",
                f"//node[@text='{country_name}']"
            ]
            
            country_elements = []
            
            for xpath in country_xpaths:
                try:
                    country_elements = new_content.xpath(xpath)
                    if country_elements:
                        self.logger.debug(f"Found country using xpath: {xpath}")
                        break
                except Exception as e:
                    self.logger.debug(f"Country xpath {xpath} failed: {str(e)}")
            
            if country_elements:
                country_element = country_elements[0]
                country_bounds = country_element.get("bounds")
                
                if country_bounds:
                    self.logger.info(f"Found {country_name}, clicking it")
                    self.emulator.click_element_by_bounds(country_bounds)
                    country_found = True
                    time.sleep(2)
                    break
                
                # If bounds are not found, try clicking the parent element
                parent_xpath = f"//node[contains(@text, '{country_name}')]/parent::node()"
                parent_elements = new_content.xpath(parent_xpath)
                
                if parent_elements and "bounds" in parent_elements[0].attrib:
                    self.logger.debug(f"Found {country_name}'s parent element, clicking it")
                    self.emulator.click_element_by_bounds(parent_elements[0].get("bounds"))
                    country_found = True
                    time.sleep(2)
                    break
                else:
                    # Last resort: Try to calculate position based on text bounds
                    try:
                        if "bounds" in country_element.attrib:
                            bounds_text = country_element.get("bounds")
                            # Parse bounds from text like "[x1,y1][x2,y2]"
                            import re
                            match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds_text)
                            if match:
                                x1, y1, x2, y2 = map(int, match.groups())
                                # Click in the middle of the element
                                center_x = (x1 + x2) // 2
                                center_y = (y1 + y2) // 2
                                self.logger.debug(f"Clicking calculated position for {country_name}: ({center_x}, {center_y})")
                                self.emulator.click_on_coordinates(center_x, center_y)
                                country_found = True
                                time.sleep(2)
                                break
                    except Exception as e:
                        self.logger.debug(f"Failed to calculate position: {str(e)}")
            
            # If country not found yet, scroll down
            if not country_found:
                self.emulator.scroll_down()
                time.sleep(1)
        
        # If all scrolls failed, go back
        if not country_found:
            self.logger.warning(f"Could not find {country_name} in the list, going back")
            self.emulator.click_back_button()
            return False
            
        return country_found

    def run_state_based(self):
        if LIMIT_REACHED_WAITING > datetime.now():
            raise RateLimitException
        self.logger.info(f"Running RealCaller search for {self.reqeust.country_code} {self.reqeust.query}")
        
        try:
            # Make sure the app is open
            self.emulator.open_app(self.get_package_name())
            time.sleep(5)
            
            # Run the automation to find the contacts
            success = self.run_automation(max_iterations=30)
            
            # Check if automation completed successfully
            if success and self.done_automation:
                if self.results:
                    self.logger.info(f"Successfully found {len(self.results)} results for {self.reqeust.query}")
                else:
                    self.logger.info(f"No results found for {self.reqeust.query} - this is a valid empty result")
                
                # Return results (might be empty if no match was found)
                return self.results
            else:
                self.logger.error(f"Failed to complete automation for {self.reqeust.query}")
                # Return empty dictionary instead of None
                return {}
                
        except Exception as e:
            self.logger.error(f"Error in run_state_based: {str(e)}")
            # Return empty dictionary on error
            return {}
        finally:
            try:
                self.emulator.force_stop_app(self.get_package_name())
                self.emulator.empty_ram_with_adb()
            except Exception:
                pass

    def toggle_search_mode(self, target_mode: str = None) -> bool:
        try:
            # Get current search mode if target mode is specified
            current_mode = None
            if target_mode:
                current_mode = self.check_current_search_mode()
                if current_mode == target_mode:
                    self.logger.info(f"Already in {target_mode} search mode, no need to toggle")
                    return True
            
            # Log the toggle action
            mode_text = f"to {target_mode}" if target_mode else "search mode"
            self.logger.info(f"Toggling {mode_text}")
            
            # Get the UI tree
            content = self.emulator.get_lxml_tree()
            
            # Define XPath patterns for the toggle button
            toggle_button_xpaths = [
                # Using exact bounds for the search toggle button beside input field (most reliable)
                "//node[@class='android.widget.Button' and @bounds='[928,296][1038,406]']",
                
                # Using parent-child relationship to target button near EditText
                "//node[@class='android.widget.EditText']/following-sibling::node[@class='android.widget.Button']",
                
                # Using specific coordinates to find the button
                "//node[@class='android.widget.Button' and contains(@bounds, '928,296')]",
                
                # More general approach with indices - less reliable but a fallback
                "//node[@class='android.widget.LinearLayout']/node[@class='android.widget.Button' and @index='2']"
            ]
            
            # Look for button by XPaths
            toggle_button = None
            for xpath in toggle_button_xpaths:
                try:
                    self.logger.debug(f"Trying to find toggle button with XPath: {xpath}")
                    toggle_button = self.emulator.get_bounds_by_xpath(content, xpath)
                    if toggle_button:
                        self.logger.debug(f"Found toggle button with XPath: {xpath}")
                        break
                except Exception as e:
                    self.logger.debug(f"XPath {xpath} failed: {str(e)}")
            
            # If button found, click it
            if toggle_button:
                self.emulator.click_element_by_bounds(toggle_button)
                self.logger.info("Clicked toggle button")
                time.sleep(2)
            else:
                # If we couldn't find the button by XPath, try coordinates
                self.logger.debug("Using hardcoded coordinates for toggle button")
                # Hardcoded coordinates for the middle of the toggle button area
                self.emulator.click_on_coordinates(983, 351)
                time.sleep(2)
            
            # Verify the mode changed
            if target_mode:
                new_content = self.emulator.get_lxml_tree()
                new_mode = self.check_current_search_mode()
                
                if new_mode == target_mode:
                    self.logger.info(f"Successfully switched to {target_mode} search mode")
                    return True
                else:
                    self.logger.warning(f"Toggle failed! Current: {new_mode}, requested: {target_mode}")
                    # Try one more time
                    self.emulator.click_on_coordinates(983, 351)
                    time.sleep(2)
                    return self.check_current_search_mode() == target_mode
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error toggling search mode: {str(e)}")
            return False
            
    # Keep the old method name for backward compatibility
    def toggle_to_name_search(self) -> bool:
        return self.toggle_search_mode(target_mode="name")

def testing() -> None:
    from Emulator import Emulator
    import traceback

    try:
        logger = setup_logger("Testing", "logs/testing.log")
        logger.info("Starting RealCaller testing with name-number-number-name pattern")
        
        e = Emulator(1, "VWXOUSDMJ75PAABE")
        
        # Test data for Kuwait and Syria with name-number-number-name pattern
        test_data = {
            "country_code": "965",  # Kuwait
            "name_queries": ["yousef", "Ahmad"],
            "number_queries": ["41041276", "90123456"],
            "country_code_2": "963",  # Syria
            "name_queries_2": ["noura", "Ahmad"],
            "number_queries_2": ["41041276", "90123456"]
        }
        all_results = {}
        # Test both countries in sequence
        for country_index, country_code in enumerate([test_data["country_code"], test_data["country_code_2"]]):
            country_suffix = "" if country_index == 0 else "_2"
            country_name = COUNTRY_NAME_MAP.get(country_code, "N/A")
            
            logger.info(f"Testing for {country_name} ({country_code}) with name-number-number-name pattern")
            # Create request objects in specific order: name, number, number, name
            test_requests = []
            
            # 1. First name test
            test_requests.append(PhoneRequest(
                id=1 + country_index*10,
                search_type=SearchTypeEnum.ByName,
                country_code=country_code,
                query=test_data[f"name_queries{country_suffix}"][0],
                status=RequestStatusEnum.NEW,
                created_at=datetime.now(),
                modified_at=datetime.now(),
            ))
            
            # 2. First number test
            test_requests.append(PhoneRequest(
                id=2 + country_index*10,
                search_type=SearchTypeEnum.ByNumber,
                country_code=country_code,
                query=test_data[f"number_queries{country_suffix}"][0],
                status=RequestStatusEnum.NEW,
                created_at=datetime.now(),
                modified_at=datetime.now(),
            ))
            
            # 3. Second number test
            test_requests.append(PhoneRequest(
                id=3 + country_index*10,
                search_type=SearchTypeEnum.ByNumber,
                country_code=country_code,
                query=test_data[f"number_queries{country_suffix}"][1],
                status=RequestStatusEnum.NEW,
                created_at=datetime.now(),
                modified_at=datetime.now(),
            ))
            
            # 4. Second name test
            test_requests.append(PhoneRequest(
                id=4 + country_index*10,
                search_type=SearchTypeEnum.ByName,
                country_code=country_code,
                query=test_data[f"name_queries{country_suffix}"][1],
                status=RequestStatusEnum.NEW,
                created_at=datetime.now(),
                modified_at=datetime.now(),
            ))
            
            # Process all queries in sequence: name, number, number, name
            for i, req in enumerate(test_requests):
                search_type = "name" if req.search_type == SearchTypeEnum.ByName else "number"
                logger.info(f"Test {i+1}/4 for {country_name}: {search_type.upper()} search for '{req.query}'")
                
                # Create a new RealCaller instance for each query
                rc = RealCaller(e, req)
                
                # Run the automation
                logger.info(f"Running automation for {search_type} query: {req.query}")
                try:
                    result = rc.run_automation()
                    logger.info(f"Automation completed with result: {result}")
                    
                    if hasattr(rc, 'results'):
                        # Success case
                        result_key = f"{country_code}-{search_type}-{req.query}"
                        all_results[result_key] = rc.results
                        
                        if rc.results:
                            result_count = len(rc.results)
                            logger.info(f"Found {result_count} results for {search_type} query: {req.query}")
                            
                            # Log top results
                            sorted_results = sorted(rc.results.items(), key=lambda x: x[1], reverse=True)
                            logger.info(f"Top results for '{req.query}':")
                            for name, count in sorted_results[:5]:
                                logger.info(f"  {name}: {count}")
                        else:
                            logger.info(f"No results found for {search_type} query: {req.query}")
                    else:
                        logger.warning(f"No results attribute found for {search_type} query: {req.query}")
                    
                    # Wait between requests to avoid rate limiting
                    if i < len(test_requests) - 1:
                        wait_time = 10
                        logger.info(f"Waiting {wait_time} seconds before next test")
                        time.sleep(wait_time) 
                except Exception as e:
                    logger.error(f"Error during {search_type} search for {req.query}: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Continue with next query despite errors
            
            if country_index == 0:  # After Kuwait, before Syria
                logger.info("Waiting 20 seconds before testing Syria...")
                time.sleep(20)
        
        logger.info("=== Testing Summary ===")
        successful_queries = len(all_results)
        logger.info(f"Completed {successful_queries}/8 tests successfully")
        
        for country_index, country_code in enumerate([test_data["country_code"], test_data["country_code_2"]]):
            country_name = COUNTRY_NAME_MAP.get(country_code, "N/A")
            logger.info(f"Results for {country_name} ({country_code}):")
            # Count name and number searches
            name_results = {k: v for k, v in all_results.items() 
                          if k.startswith(f"{country_code}-name-")}
            number_results = {k: v for k, v in all_results.items() 
                            if k.startswith(f"{country_code}-number-")}
            logger.info(f"  Name searches: {len(name_results)}/2 successful")
            for key in name_results:
                query = key.split('-')[2]
                results = name_results[key]
                logger.info(f"    Name query: {query}, Results count: {len(results) if results else 0}")     
            logger.info(f"  Number searches: {len(number_results)}/2 successful")
            for key in number_results:
                query = key.split('-')[2]
                results = number_results[key]
                logger.info(f"    Number query: {query}, Results count: {len(results) if results else 0}")    
    except Exception as ex:
        logger.error(f"Error during testing: {str(ex)}")
        logger.error(traceback.format_exc())    
    logger.info("Testing complete")

if __name__ == "__main__":
    testing()