from dotenv import load_dotenv
import json, argparse
from typing import List
load_dotenv(".env")
from os import getenv
from Emulator import Emulator
from error import log, setup_logger
from utils import get_devices, load_config
main_logger = setup_logger("main", "logs/main.log")
parser = argparse.ArgumentParser(description="")
parser.add_argument("--skip-bad-devices", type=str)
args = parser.parse_args()
log(main_logger, "info", "started script")
CONFIG = load_config()
with open("devices.json", "r", encoding="utf-8") as f: DEVICES_TO_USE = json.loads(f.read())
DEVICES_TO_USE_DICT = {}
for device in DEVICES_TO_USE["emulators"] : DEVICES_TO_USE_DICT[device] = "emulator"
for device in DEVICES_TO_USE["pdevices"] : DEVICES_TO_USE_DICT[device] = "physical"
active_devices = get_devices()
device_id = int(getenv("DEVICE_ID_START_INDEX"))
emulators : List[Emulator]= []
not_working_devices = []
for i, device in enumerate(list(DEVICES_TO_USE_DICT.keys())) :
    if device not in active_devices :
        if args.skip_bad_devices :
            not_working_devices.append(device)
            continue
        raise Exception(f"couldn't find an active device of name {device}. remove it from devices list or add --skip-bad-devices")
    log(main_logger, "status", f"starting device of name {device}")
    device_id+=1    
    e=Emulator(device_id, device, CONFIG["tasks"][i] if i<len(CONFIG["tasks"]) else CONFIG["tasks"][-1], CONFIG["use proxy app"], CONFIG["stop emulator on app exception"])
    if not e.test_connectivity()[0] :
        if args.skip_bad_devices :
            not_working_devices.append(e.full_name_and_model)
            continue
        raise Exception(f"device {device}'s not responding to adb commands. remove it from devices list or add --skip-bad-devices")
    else : emulators.append(e)
if not_working_devices : log(main_logger, "info", f"skipping not working devices {', '.join(not_working_devices)}")

threads = [emulator.run() for emulator in emulators]
if len(threads)==0 :
    log(main_logger, "error", "couldn't find any active devices")
    exit()
else :
    log(main_logger, "info", f"started {str(len(threads))} devices\ncollecting {'-'.join([', '.join(app_list) for app_list in CONFIG['tasks']])}")
for thread in threads : thread.join()
log(main_logger, "error", "reached end of main thread")