import re
import subprocess
import time
from pathlib import Path

from Emulator import Emulator


class VirtualMasterLauncher:
    PACKAGE = "com.clone.android.dual.space"
    APK_FILENAME = "virtualmaster.apk"
    MONKEY_LAUNCH_WAIT = 2

    def __init__(self, emulator: Emulator, device_id: str) -> None:
        self.emulator = emulator
        self.DEVICE_ID = device_id
        self.apk_path = Path("apks") / self.APK_FILENAME

    def open_app(self) -> None:
        print(f"Launching '{self.PACKAGE}' via monkey...")

        cmd = [
            "adb",
            "-s",
            self.DEVICE_ID,
            "shell",
            "monkey",
            "-p",
            self.PACKAGE,
            "-c",
            "android.intent.category.LAUNCHER",
            "1",
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0 or "Error" in result.stdout:
            print("STDOUT:", result.stdout.strip())
            print("STDERR:", result.stderr.strip())
            self.emulator.click_back_button()
            return

        time.sleep(self.MONKEY_LAUNCH_WAIT)
        self.press_start_while_visible()

    def _adb_shell(self, *args):
        return subprocess.run(
            ["adb", "-s", self.DEVICE_ID, "shell", *args],
            capture_output=True,
            text=True,
        )

    def _go_to_home_and_clear_tasks(self):
        self._adb_shell("input", "keyevent", "KEYCODE_HOME")
        self._adb_shell("input", "keyevent", "KEYCODE_APP_SWITCH")
        size_output = self._adb_shell("wm", "size").stdout
        if match := re.search(r"(\d+)x(\d+)", size_output):
            x = int(match.group(1)) // 2
            y1, y2 = int(int(match.group(2)) * 3 / 4), int(int(match.group(2)) / 4)
            self._adb_shell("input", "swipe", str(x), str(y1), str(x), str(y2))

    def press_start_while_visible(self):
        xpaths = {
            "start": "//node[@resource-id='com.clone.android.dual.space:id/start' and @text='Start']",
            "watch_ads": "//node[@resource-id='android:id/button2' and @text='Watch Ads']",
            "vm_indicator": "//node[@package='com.clone.android.dual.space' and @text='VM']",
        }

        while True:
            content = self.emulator.get_lxml_tree()
            if not self.emulator.get_bounds_by_xpath(
                content, xpaths["vm_indicator"], raise_exception=False
            ):
                print("Expected page not detected, relaunching app...")
                self._go_to_home_and_clear_tasks()
                self.open_app()
                return

            start_bounds = self.emulator.get_bounds_by_xpath(
                content, xpaths["start"], raise_exception=False, ignore_logs=True
            )
            if not start_bounds:
                time.sleep(2)
                continue

            self.emulator.click_element_by_bounds(start_bounds)
            time.sleep(1)
            content = self.emulator.get_lxml_tree()
            watch_ads_bounds = self.emulator.get_bounds_by_xpath(
                content, xpaths["watch_ads"], raise_exception=False
            )
            if watch_ads_bounds:
                self.emulator.click_element_by_bounds(watch_ads_bounds)

                while not self.emulator.get_bounds_by_xpath(
                    self.emulator.get_lxml_tree(),
                    xpaths["start"],
                    raise_exception=False,
                ):
                    time.sleep(10)
            time.sleep(15)  # small delay to avoid spamming the server

    def run(self) -> None:
        self.open_app()


# Usage
if __name__ == "__main__":
    DEVICE_ID = "10DE7A0C56000B2"
    emulator = Emulator(1, DEVICE_ID)
    master_app = VirtualMasterLauncher(emulator, DEVICE_ID)
    master_app.run()
