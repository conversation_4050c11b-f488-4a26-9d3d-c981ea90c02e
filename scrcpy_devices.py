import argparse
import json
import math
import os
import subprocess
import threading
import time
from typing import List, Optional, Tuple

with open("devices.json", "r") as f:
    mydata = json.load(f)
pdevices = mydata["working_devices"]
# vms = mydata["VMs"]
# vm_master = mydata["master_devices"]
devices = pdevices  # + vms + vm_master


def get_connected_devices() -> List[str]:
    """
    Get all connected ADB devices

    Returns:
        List of device serial numbers
    """
    try:
        result = subprocess.run(
            ["adb", "devices"], capture_output=True, text=True, check=True
        )

        connected_devices = []
        lines = result.stdout.strip().split("\n")[
            1:
        ]  # Skip first line "List of devices attached"

        for line in lines:
            if line.strip() and "\tdevice" in line:
                device_id = line.split("\t")[0]
                connected_devices.append(device_id)

        return connected_devices

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"Error getting connected devices: {e}")
        return []


def get_screen_size() -> Tuple[int, int]:
    """
    Get the screen resolution on Linux using xrandr

    Returns:
        Tuple of (width, height) in pixels
    """
    try:
        # Try xrandr first (most common)
        result = subprocess.run(
            ["xrandr", "--current"], capture_output=True, text=True, check=True
        )

        # Parse xrandr output to find current resolution
        for line in result.stdout.split("\n"):
            if "*" in line and "x" in line:
                # Extract resolution from line like "1920x1080  59.96*+"
                resolution = line.split()[0]
                width, height = map(int, resolution.split("x"))
                return width, height

    except (subprocess.CalledProcessError, FileNotFoundError, ValueError):
        pass

    try:
        # Fallback to xdpyinfo
        result = subprocess.run(
            ["xdpyinfo"], capture_output=True, text=True, check=True
        )

        for line in result.stdout.split("\n"):
            if "dimensions:" in line:
                # Extract from line like "  dimensions:    1920x1080 pixels (508x285 millimeters)"
                dims = line.split("dimensions:")[1].split()[0]
                width, height = map(int, dims.split("x"))
                return width, height

    except (subprocess.CalledProcessError, FileNotFoundError, ValueError):
        pass

    # Default fallback resolution
    print("Warning: Could not detect screen size, using default 1920x1080")
    return 1920, 1080


def calculate_optimal_layout(
    num_devices: int, screen_width: int, screen_height: int
) -> Tuple[int, int, int, int, int, int]:
    """
    Calculate optimal window dimensions and layout for given number of devices

    Args:
        num_devices: Number of devices to display
        screen_width: Screen width in pixels
        screen_height: Screen height in pixels

    Returns:
        Tuple of (window_width, window_height, windows_per_row, windows_per_col, margin_x, margin_y)
    """
    if num_devices == 0:
        return 150, 200, 1, 1, 10, 10

    # Reserve space for taskbar and window decorations
    usable_width = screen_width - 100
    usable_height = screen_height - 150

    # Target aspect ratio for phone screens (roughly 9:16 or similar)
    target_aspect_ratio = 9 / 16

    # Calculate optimal grid dimensions
    # Start with square root and adjust
    sqrt_devices = math.sqrt(num_devices)

    best_layout = None
    best_waste = float("inf")

    # Try different grid configurations
    for cols in range(1, num_devices + 1):
        rows = math.ceil(num_devices / cols)

        # Calculate window dimensions for this grid
        margin_x = 10
        margin_y = 10

        available_width_per_window = (usable_width - (cols - 1) * margin_x) / cols
        available_height_per_window = (usable_height - (rows - 1) * margin_y) / rows

        # Determine window size based on target aspect ratio
        if (
            available_width_per_window / available_height_per_window
            > target_aspect_ratio
        ):
            # Width is the limiting factor
            window_height = int(available_height_per_window)
            window_width = int(window_height * target_aspect_ratio)
        else:
            # Height is the limiting factor
            window_width = int(available_width_per_window)
            window_height = int(window_width / target_aspect_ratio)

        # Ensure minimum size
        window_width = max(window_width, 100)
        window_height = max(window_height, 150)

        # Calculate wasted space
        total_window_area = num_devices * window_width * window_height
        total_screen_area = usable_width * usable_height
        waste = total_screen_area - total_window_area

        # Prefer layouts that are closer to square and waste less space
        aspect_penalty = abs(cols / rows - 1) * 1000
        total_penalty = waste + aspect_penalty

        if total_penalty < best_waste:
            best_waste = total_penalty
            best_layout = (window_width, window_height, cols, rows, margin_x, margin_y)

    if best_layout:
        return best_layout

    # Fallback
    return 150, 200, min(8, num_devices), math.ceil(num_devices / 8), 10, 10


def start_scrcpy(
    device_id: str, position_index: int = 0, layout_config: Optional[Tuple] = None
) -> Optional[subprocess.Popen]:
    """
    Start scrcpy for a specific device with window positioned in a grid layout

    Args:
        device_id: The device serial number
        position_index: Index used to calculate the window position in grid
        layout_config: Tuple of (window_width, window_height, windows_per_row, windows_per_col, margin_x, margin_y)

    Returns:
        The subprocess.Popen object or None if failed
    """
    try:
        # Use provided layout config or calculate default
        if layout_config:
            (
                window_width,
                window_height,
                windows_per_row,
                windows_per_col,
                margin_x,
                margin_y,
            ) = layout_config
        else:
            window_width, window_height = 150, 200
            windows_per_row, margin_x, margin_y = 8, 10, 10

        # Create a unique window title with the device ID
        window_title = f"{device_id}"

        # Calculate position based on index (creates a grid layout)
        row = position_index // windows_per_row
        col = position_index % windows_per_row

        # Calculate pixel position
        x_position = (
            col * (window_width + margin_x) + 50
        )  # 50px offset from screen edge
        y_position = (
            row * (window_height + margin_y) + 50
        )  # 50px offset from screen edge

        # Configure scrcpy command with options for better performance
        cmd = [
            "scrcpy",
            "--serial",
            device_id,
            "--window-title",
            window_title,
            "--max-fps",
            "15",  # Limit to 15 FPS to reduce CPU usage
            "--window-width",
            str(window_width),
            "--window-height",
            str(window_height),
            "--window-x",
            str(x_position),  # Set window X position
            "--window-y",
            str(y_position),  # Set window Y position
        ]

        # Start the process
        process = subprocess.Popen(
            cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL
        )

        print(
            f"Started scrcpy for device {device_id} at position ({x_position}, {y_position}) "
            f"size ({window_width}x{window_height})"
        )

        return process

    except Exception as e:
        print(f"Error starting scrcpy for device {device_id}: {e}")
        return None


def start_all_devices(
    device_list: List[str], max_concurrent: int = 5
) -> List[subprocess.Popen]:
    """
    Start scrcpy for all devices with optimal layout and a limit on concurrent starts

    Args:
        device_list: List of device IDs
        max_concurrent: Maximum number of devices to start concurrently

    Returns:
        List of subprocess.Popen objects for started processes
    """
    if not device_list:
        print("No devices to start")
        return []

    # Get screen size and calculate optimal layout
    screen_width, screen_height = get_screen_size()
    layout_config = calculate_optimal_layout(
        len(device_list), screen_width, screen_height
    )

    (
        window_width,
        window_height,
        windows_per_row,
        windows_per_col,
        margin_x,
        margin_y,
    ) = layout_config

    print(f"Screen size: {screen_width}x{screen_height}")
    print(
        f"Optimal layout: {windows_per_row}x{windows_per_col} grid, "
        f"window size: {window_width}x{window_height}"
    )
    print(f"Starting {len(device_list)} devices...")

    threads = []
    processes = []

    for i, device_id in enumerate(device_list):
        # Create and start a thread for each device, passing the position index and layout
        def start_device(device_id=device_id, idx=i, layout=layout_config):
            process = start_scrcpy(device_id, idx, layout)
            if process:
                processes.append(process)

        thread = threading.Thread(target=start_device)
        threads.append(thread)
        thread.start()

        # If we've reached the max concurrent limit, wait for all to complete
        if (i + 1) % max_concurrent == 0 or i == len(device_list) - 1:
            for t in threads:
                t.join()
            threads = []
            time.sleep(1)  # Small delay between batches

    print(f"Successfully started {len(processes)} scrcpy instances")
    return processes


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Start scrcpy for devices")
    parser.add_argument(
        "--all",
        action="store_true",
        help="Get all connected devices and start scrcpy for them",
    )
    args = parser.parse_args()

    # Determine which devices to use
    if args.all:
        device_list = get_connected_devices()
        if not device_list:
            print("No connected devices found")
            exit(1)
        print(f"Found {len(device_list)} connected devices: {device_list}")
    else:
        device_list = devices
        print(f"Using {len(device_list)} devices from config file")

    # Start all devices with dynamic layout
    processes = start_all_devices(device_list)

    print("\nPress Ctrl+C to stop all scrcpy instances")
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        answer = input("do you want to stop all scrcpy instances? (y/n) ")
        if answer != "n":
            print("\nStopping all scrcpy instances...")
            # Kill all scrcpy processes
            subprocess.run(["pkill", "scrcpy"], check=False)
            print("All scrcpy instances stopped")

# Alternative way to kill all: "pkill scrcpy"
