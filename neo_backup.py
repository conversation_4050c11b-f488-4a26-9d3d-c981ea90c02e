from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from Emulator import Emulator
import time

from error import log, setup_logger
from schemas import Country<PERSON><PERSON><PERSON><PERSON>, SearchTypeEnum
from Task import AppStateBase, FailedAutomationException, Task
from utils import (
    format_xpath_attrs,
    format_xpath_attrs_exact,
    load_config,
    process_and_upload_backup,
)

logger = setup_logger("neo_backup", "logs/neo_backup.log")

CONFIG = load_config()
DEBUGGING = False


class NeoBackupAppState(AppStateBase):
    # App States
    INTRO_SCREEN = "intro_screen"
    HOME_PAGE = "home_page"


class NeoBackup(Task):
    logger = logger
    AppState = NeoBackupAppState
    XPATH_PATTERNS = {
        AppState.INTRO_SCREEN: [format_xpath_attrs(text="Let's go!")],
        AppState.HOME_PAGE: [format_xpath_attrs(text="Homepage")],
    }

    def __init__(
        self,
        emulator: Emulator,
        target_package_name: str,
        backup_info: dict = {},
    ):
        super().__init__(
            emulator,
            None,
        )
        self.state_handlers = {
            self.AppState.UNKNOWN: self.handle_unknown_state,
            self.AppState.INTRO_SCREEN: self.intro_screen_handler,
            self.AppState.HOME_PAGE: self.home_page_handler,
        }
        self.results = {}
        self.done_automation = False
        self.target_package_name = target_package_name
        self.backup_info = backup_info

    def intro_screen_handler(self):
        content = self.emulator.get_lxml_tree()
        letsgo = format_xpath_attrs(text="Let's go!")
        self.emulator.click_by_xpath_direct(content, letsgo)

    def home_page_handler(self):
        content = self.emulator.get_lxml_tree()
        search = format_xpath_attrs(content_desc="Search")
        self.emulator.click_by_xpath_direct(content, search)
        self.emulator.clear_text_adb_keyboard()
        self.emulator.type_text_adb_keyboard(self.target_package_name)
        self.emulator.click_enter()
        time.sleep(10)
        content = self.emulator.get_lxml_tree()
        target_xpath = format_xpath_attrs(text=self.target_package_name)
        self.emulator.click_by_xpath_direct(content, target_xpath)
        time.sleep(6)
        self.emulator.scroll_down(distance=1200)
        time.sleep(6)
        self.emulator.scroll_down(distance=1200)
        time.sleep(6)
        content = self.emulator.get_lxml_tree()
        backup = format_xpath_attrs_exact(text="Backup")
        self.emulator.click_by_xpath_direct(content, backup)
        time.sleep(4)

        content = self.emulator.get_lxml_tree()
        check_boxes = content.xpath("//node[@class='android.widget.CheckBox']")
        assert len(check_boxes) == 6
        if check_boxes[0].get("checked") == "true":
            self.emulator.direct_click_on_node(check_boxes[0])
        time.sleep(2)
        for check_box in check_boxes[1:]:
            if check_box.get("checked") == "false":
                self.emulator.direct_click_on_node(check_box)
                time.sleep(1.5)

        ok_button = format_xpath_attrs_exact(text="OK")
        self.emulator.click_by_xpath_direct(content, ok_button)

        # Wait for backup process to complete
        time.sleep(20)  # Initial wait for backup to start
        # Pull the backup files and upload them
        self.pull_and_upload_backup()
        self.done_automation = True
        print("done automation:", self.done_automation)
        self.results = {}

    def pull_and_upload_backup(self):
        """
        Pulls backup files from the device and uploads them to the endpoint.
        Uses the new split functionality: Emulator.pull_backup + utils.process_and_upload_backup
        """
        try:
            log(
                self.logger,
                "info",
                f"Starting backup extraction for {self.target_package_name}",
            )

            # We know the backup will be in the backup_folder directory on sdcard
            remote_path = f"/sdcard/backup_folder/{self.target_package_name}"

            # Step 1: Pull the backup data into memory
            file_data, metadata = self.emulator.pull_backup(
                package_name=self.target_package_name,
                remote_path=remote_path,
            )

            if not file_data:
                log(
                    self.logger,
                    "error",
                    f"Failed to pull backup data for {self.target_package_name}",
                )
                return False

            log(
                self.logger,
                "info",
                f"Successfully pulled backup with {len(file_data)} files",
            )

            # Step 2: Process and upload the backup data

            upload_success = process_and_upload_backup(
                file_data=file_data, metadata={**metadata, **self.backup_info}
            )

            if upload_success:
                log(
                    self.logger,
                    "info",
                    f"Successfully processed and uploaded backup for {self.target_package_name}",
                )
            else:
                log(
                    self.logger,
                    "error",
                    f"Failed to process and upload backup for {self.target_package_name}",
                    file=file_data,
                    filename=metadata["archive_name"],
                )

            return upload_success

        except Exception as e:
            log(self.logger, "error", f"Error in pull_and_upload_backup: {str(e)}")
            return False

    def handle_unknown_state(self):
        content = self.emulator.get_lxml_tree()

        if len(content.xpath("//node")) == 1:
            # this case when the adb keyboard overlays the app
            self.emulator.click_back_button()
            return
        return super().handle_unknown_state()

    def run_state_based(self):
        self.emulator.clean_directory("/sdcard/backup_folder")
        self.emulator.force_stop_app(self.get_package_name())
        return super().run_state_based()

    @staticmethod
    def get_name():
        return "neo_backup"

    @staticmethod
    def get_package_name():
        return "com.machiav3lli.backup"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        return []

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        return []

    def scroll_and_fetch_names(self, phone):
        return super().scroll_and_fetch_names(phone)


class NeoBackupRestore(NeoBackup):
    """Class for handling NeoBackup restore operations."""

    def __init__(self, emulator: Emulator, target_package_name: str):
        super().__init__(emulator, target_package_name)
        self.state_handlers = {
            **self.state_handlers,
            self.AppState.HOME_PAGE: self.home_page_handler,
        }

    def run_state_based(self):
        self.emulator.force_stop_app(self.get_package_name())
        time.sleep(1)
        self.emulator.open_app(self.get_package_name())
        try:
            self.run_automation()
            if not self.done_automation:
                raise FailedAutomationException
            return self.results
        finally:
            try:
                self.emulator.empty_ram_with_adb()
            except Exception:
                pass

    def home_page_handler(self):
        try:
            content = self.emulator.get_lxml_tree()
            search = format_xpath_attrs(content_desc="Search")
            self.emulator.click_by_xpath_direct(content, search)
            self.emulator.clear_text_adb_keyboard()
            self.emulator.type_text_adb_keyboard(self.target_package_name)
            self.emulator.click_enter()
            time.sleep(8)
            content = self.emulator.get_lxml_tree()
            target_xpath = format_xpath_attrs(text=self.target_package_name)
            self.emulator.click_by_xpath_direct(content, target_xpath)
            time.sleep(8)

            self.emulator.scroll_down()
            time.sleep(3)
            self.emulator.scroll_down()
            time.sleep(7)

            content = self.emulator.get_lxml_tree()
            restore = format_xpath_attrs_exact(**{"content-desc": "Restore"})
            self.emulator.click_by_xpath_direct(content, restore)

            content = self.emulator.get_lxml_tree()
            check_boxes = content.xpath("//node[@class='android.widget.CheckBox']")

            # Ensure we have the expected number of checkboxes
            # assert len(check_boxes) == 3, (
            #     f"Expected 3 checkboxes, found {len(check_boxes)}"
            # )

            # Handle remaining checkboxes
            for check_box in check_boxes:
                if check_box.get("checked") == "false":
                    self.emulator.direct_click_on_node(check_box)
                    time.sleep(1)

            ok_button = format_xpath_attrs_exact(text="OK")
            self.emulator.click_by_xpath_direct(content, ok_button)

            # Initial wait for restore to start
            time.sleep(10)

            # Wait for restore completion dialog

            self.done_automation = True
            log(
                self.logger,
                "info",
                f"Restore process completed for {self.target_package_name}",
            )

        except Exception as e:
            log(self.logger, "error", f"Error during restore process: {str(e)}")
            self.results = {
                "status": "failed",
                "error": str(e),
                "package": self.target_package_name,
            }
            raise


def testing():
    from Emulator import Emulator

    e = Emulator(1, "emulator-5554")
    neo = NeoBackup(e, "app.source.getcontact", {"number": "+573018456664"})
    neo.run_state_based()
    # neo.home_page_handler()


if __name__ == "__main__":
    testing()
    # neo = NeoBackupRestore(Emulator(1, "emulator-5554"), "app.source.getcontact")
    # neo.run_state_based()
