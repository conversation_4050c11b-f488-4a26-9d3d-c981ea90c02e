from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from Emulator import Emulator

import re
import time
from datetime import datetime
from traceback import format_exc

import xml_tree_cursor
from constants import COUNTRY_NAME_MAP
from error import setup_logger
from schemas import CountryCodeEnum, PhoneRequest, RequestStatusEnum, SearchTypeEnum
from Task import FailedAutomationException, NotFoundException, Task
from utils import load_config

CONFIG = load_config()
SLEEP_SHORT = 2
SLEEP_LONG_AFTER_REBOOT = 20


class Numberbook_Alkhalij(Task):

    def __init__(self, emulator: Emulator, request: PhoneRequest):
        self.logger = setup_logger(
            f"Numberbook_Alkhalij_{emulator.device_name}",
            f"logs/Numberbook_Alkhalij_{emulator.device_name}.log",
        )
        super().__init__(emulator=emulator, request=request)
        self.logger.debug("Initializing Numberbook_Alkhalij with request: %s", request)
        self.emulator.open_phone()
        self.results = {}
        self.done_automation = False

        if not self.check_if_installed(emulator.get_third_party_apps()):
            self.logger.info("APK not installed, installing from apks directory...")
            apk_dir = "apks/com.cuutt.gcc"
            apk_files = [
                f"{apk_dir}/com.cuutt.gcc.apk",
                f"{apk_dir}/config.en.apk",
                f"{apk_dir}/config.mdpi.apk",
                f"{apk_dir}/config.arm64_v8a.apk",
                f"{apk_dir}/config.zh.apk",
            ]
            emulator.install_multi_app(apk_files)
            if not self.check_if_installed(emulator.get_third_party_apps()):
                raise Exception(f"Failed to install APK from {apk_dir}")
            self.logger.info("APK installed successfully")

    @classmethod
    def requires_root(cls) -> bool:
        """Override to specify that this task requires root access."""
        return True

    def cleared_app_data_init(self) -> None:
        self.logger.info("Clearing app data for package: %s", self.package_name)
        """Clear app data"""
        self.emulator.clear_data(self.package_name)

    def app_open_step(self):
        self.logger.info("Opening app: %s", self.package_name)
        """Open the app with initial setup and resolve popups."""
        self.emulator.open_app(self.package_name)
        time.sleep(SLEEP_SHORT)
        self.resolve_all_popups()

    def resolve_all_popups(self):
        self.logger.debug("Resolving all popups")
        """Resolve common popups that appear on app start."""
        self.resolve_permission_popup()
        self.resolve_better_results_popup()
        self.resolve_block_popup()

    def get_remaining_searches(self) -> int | None:
        self.logger.debug("Fetching remaining searches")
        """Extract and return the number of remaining searches from the UI hierarchy."""
        ui_dump = self.emulator.get_ui_hierarchy()
        node_tree = self.emulator.get_node_tree(ui_dump)
        for node in node_tree.findall(".//node"):
            content_desc = node.attrib.get("content-desc", "")
            if content_desc and "Remaining Searches :" in content_desc:
                match = re.search(r"\d+", content_desc)
                number = int(match.group()) if match else None
                return number
        return None

    def change_current_region(self, country_code: str):
        """
        Change the current region to the specified country.

        This method first deselects the current region if it is active, then selects the specified country.
        """
        self.logger.info(
            "Changing current region to %s", COUNTRY_NAME_MAP[country_code]
        )

        try:
            content = self.emulator.get_lxml_tree()
        except Exception as e:
            self.logger.info("Failed to parse UI dump (initial): %s", e)
            return

        if len(content.xpath("//node")) == 1:
            self.logger.info("Only one node found in UI tree. Going back and retrying.")
            self.emulator.click_back_button()
            time.sleep(SLEEP_SHORT)
            try:
                content = self.emulator.get_lxml_tree()
            except Exception as e:
                self.logger.info("Failed to parse UI dump (after back): %s", e)
                return

        current_region_xpath = (
            "//node[@index='0' and @class='android.view.View' and @package='com.cuutt.gcc'"
            " and string-length(@content-desc) > 0]"
        )
        current_bounds = self.emulator.get_bounds_by_xpath(
            content, current_region_xpath
        )

        if current_bounds:
            self.logger.info("Current region active. Attempting to deselect.")
            self.emulator.click_element_by_bounds(current_bounds)
            time.sleep(SLEEP_SHORT)

        try:
            content = self.emulator.get_lxml_tree()
        except Exception as e:
            self.logger.info("Failed to parse UI dump (after deselect): %s", e)
            return

        country_xpath = f"//node[@content-desc='{COUNTRY_NAME_MAP[country_code]}']"
        country_bounds = self.emulator.get_bounds_by_xpath(content, country_xpath)

        if country_bounds:
            self.logger.info("Country found. Selecting it.")
            self.emulator.click_element_by_bounds(country_bounds)
            time.sleep(SLEEP_SHORT)
            try:
                self.emulator.click_element_by_bounds(country_bounds)
                time.sleep(SLEEP_SHORT)
            except Exception:
                pass

        else:
            self.logger.info(
                "Country region '%s' not found. Pressing back.",
                COUNTRY_NAME_MAP[country_code],
            )
            back_button_xpath = (
                "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' "
                "and @content-desc='Back']"
            )
            back_button_bounds = self.emulator.get_bounds_by_xpath(
                content, back_button_xpath
            )
            if back_button_bounds:
                self.emulator.click_element_by_bounds(back_button_bounds)
                time.sleep(SLEEP_SHORT)

    def _handle_warning_screen(self, retry_function=None):
        content = self.emulator.get_lxml_tree()
        warning_xpath = "//node[@content-desc='Warning']"
        warning_node = content.xpath(warning_xpath)
        if warning_node:
            self.logger.info(
                "Warning screen detected. Changing device ID and retrying search."
            )
            from DeviceIDChanger import DeviceIDChanger

            device_id_changer = DeviceIDChanger(
                emulator=self.emulator,
                target_package="com.cuutt.gcc",
                target_apk="com.cuutt.gcc.apk",
            )
            device_id_changer.run()
            self.logger.info(
                "Device ID changed successfully. Waiting for app to restart..."
            )
            time.sleep(SLEEP_LONG_AFTER_REBOOT)
            self.emulator.open_phone()
            self.cleared_app_data_init()
            self.app_open_step()
            self.change_current_region(self.reqeust.country_code)
            if retry_function:
                retry_function()

    def scroll_and_fetch_numbers(self):
        self.logger.info(
            "Scrolling and fetching names for query: %s", self.reqeust.query
        )
        """Perform search and scrape results"""
        content = self.emulator.get_lxml_tree()
        edittext_xpath = "//node[@class='android.widget.EditText' and @package='com.cuutt.gcc' and @NAF='true']"
        try:
            edittext_bounds = self.emulator.get_bounds_by_xpath(content, edittext_xpath)
            if edittext_bounds:
                search_by_name_xpath = "//node[@class='android.widget.RadioButton' and @package='com.cuutt.gcc'][2]"
                self.emulator.click_by_xpath(content, search_by_name_xpath)
                time.sleep(SLEEP_SHORT)
                self.emulator.click_element_by_bounds(edittext_bounds)
                time.sleep(SLEEP_SHORT)
                self.emulator.clear_text_adb_keyboard()
                time.sleep(SLEEP_SHORT)
                self.emulator.type_text_adb_keyboard(self.reqeust.query)
                time.sleep(SLEEP_SHORT)
                content = self.emulator.get_lxml_tree()
                search_button_xpath = "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' and @content-desc='Search']"
                self.emulator.click_by_xpath(content, search_button_xpath)
                time.sleep(SLEEP_SHORT)
        except NotFoundException:
            self.logger.warning("EditText not found, skipping input.")

        # Handle no results case
        content = self.emulator.get_lxml_tree()
        no_results = content.xpath("//node[@content-desc='No results for this name']")
        if no_results:
            close_button_bounds = self.emulator.get_bounds_by_xpath(
                content,
                "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' "
                "and @content-desc='Close']",
            )
            if close_button_bounds:
                self.emulator.click_element_by_bounds(close_button_bounds)
                time.sleep(SLEEP_SHORT)
                return
        # Handle consent popup (optional)
        try:
            consent_button_xpath = "//node[@text='Do not consent' and @class='android.widget.Button' and @package='com.cuutt.gcc']"
            consent_button_bounds = self.emulator.get_bounds_by_xpath(
                content, consent_button_xpath, raise_exception=False
            )
            if consent_button_bounds:
                self.emulator.click_element_by_bounds(consent_button_bounds)
                time.sleep(SLEEP_SHORT)
        except Exception:
            pass
        try:
            # Click "Show More" for all results
            unique_numbers = set()

            while True:
                content = self.emulator.get_lxml_tree()

                # Scrape numbers BEFORE clicking "Show More"
                country_code = self.reqeust.country_code
                name_nodes = content.xpath(
                    f"//node[@class='android.view.View' and contains(@content-desc, '{country_code}')]"
                )
                for node in name_nodes:
                    content_desc = node.get("content-desc", "")
                    numbers_in_desc = re.findall(r"\d{8,}", content_desc)
                    for number in numbers_in_desc:
                        unique_numbers.add(number)

                show_more_xpath = (
                    "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' "
                    "and @content-desc='Show More']"
                )
                try:
                    show_more_bounds = self.emulator.get_bounds_by_xpath(
                        content, show_more_xpath
                    )
                    if show_more_bounds:
                        self.emulator.click_element_by_bounds(show_more_bounds)
                        time.sleep(SLEEP_SHORT)
                    else:
                        break
                except Exception:
                    break

            self.results = {x: 1 for x in unique_numbers}
            if unique_numbers or no_results:
                self.done_automation = True
            if unique_numbers:
                self.logger.info("All unique Numbers found:")
                for number in unique_numbers:
                    self.logger.debug(f"Number: {number}")
            else:
                self.logger.info("No numbers found.")

            # Go back to search screen
            content = self.emulator.get_lxml_tree()
            back_button_xpath = (
                "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' "
                "and @content-desc='Back']"
            )
            back_button_bounds = self.emulator.get_bounds_by_xpath(
                content, back_button_xpath
            )
            if back_button_bounds:
                self.emulator.click_element_by_bounds(back_button_bounds)
                time.sleep(SLEEP_SHORT)

        except Exception:
            self.logger.error("Error processing Name: %s", format_exc())

        # Detect specific screen and change device ID if necessary
        self._handle_warning_screen(retry_function=self.scroll_and_fetch_numbers)

    def scroll_and_fetch_names(self):
        self.logger.info(
            "Scrolling and fetching names for query: %s", self.reqeust.query
        )
        content = self.emulator.get_lxml_tree()
        edittext_xpath = (
            "//node[@class='android.widget.EditText' and @package='com.cuutt.gcc']"
        )
        try:
            edittext_bounds = self.emulator.get_bounds_by_xpath(content, edittext_xpath)
            if edittext_bounds:
                search_by_number_xpath = "//node[@class='android.widget.RadioButton' and @package='com.cuutt.gcc'][1]"
                self.emulator.click_by_xpath(content, search_by_number_xpath)
                time.sleep(SLEEP_SHORT)
                self.emulator.click_element_by_bounds(edittext_bounds)
                time.sleep(SLEEP_SHORT)
                self.emulator.clear_text_adb_keyboard()
                time.sleep(SLEEP_SHORT)
                self.emulator.type_text_adb_keyboard(self.reqeust.query)
                time.sleep(SLEEP_SHORT)
                content = self.emulator.get_lxml_tree()
                search_button_xpath = "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' and @content-desc='Search']"
                self.emulator.click_by_xpath(content, search_button_xpath)
                time.sleep(SLEEP_SHORT)
        except NotFoundException:
            self.logger.warning("EditText not found, skipping input.")
            return

        # Handle no results case
        content = self.emulator.get_lxml_tree()
        no_results = content.xpath("//node[@content-desc='No results for this number']")
        if no_results:
            close_button_bounds = self.emulator.get_bounds_by_xpath(
                content,
                "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' and @content-desc='Close']",
            )
            if close_button_bounds:
                self.emulator.click_element_by_bounds(close_button_bounds)
                time.sleep(SLEEP_SHORT)
            return

        # Handle consent popup (optional)
        consent_button_xpath = "//node[@text='Do not consent' and @class='android.widget.Button' and @package='com.cuutt.gcc']"
        consent_button_bounds = self.emulator.get_bounds_by_xpath(
            content, consent_button_xpath, raise_exception=False
        )
        if consent_button_bounds:
            self.emulator.click_element_by_bounds(consent_button_bounds)
            time.sleep(SLEEP_SHORT)

        unique_names = set()

        try:
            while True:
                content = self.emulator.get_lxml_tree()

                name_nodes = content.xpath("//node[contains(@content-desc, '\n')]")
                for node in name_nodes:
                    content_desc = node.get("content-desc", "")
                    parts = content_desc.split("\n")
                    if len(parts) > 1:
                        name = parts[0].strip()
                        if (
                            name
                            and name != "Remaining Searches :"
                            and name != "Select the country :"
                            and name not in unique_names
                        ):
                            unique_names.add(name)

                show_more_xpath = "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' and @content-desc='Show More']"
                try:
                    show_more_bounds = self.emulator.get_bounds_by_xpath(
                        content, show_more_xpath
                    )
                    if show_more_bounds:
                        self.emulator.click_element_by_bounds(show_more_bounds)
                        time.sleep(SLEEP_SHORT)
                    else:
                        break
                except Exception:
                    break

            self.results = {name: 1 for name in unique_names}
            if unique_names or no_results:
                self.done_automation = True
            if unique_names:
                self.logger.info("All unique names found:")
                for name in unique_names:
                    self.logger.debug(f"Name: {name}")
            else:
                self.logger.info("No names found.")

            # Go back to search screen
            content = self.emulator.get_lxml_tree()
            back_button_xpath = "//node[@class='android.widget.Button' and @package='com.cuutt.gcc' and @content-desc='Back']"
            back_button_bounds = self.emulator.get_bounds_by_xpath(
                content, back_button_xpath
            )
            if back_button_bounds:
                self.emulator.click_element_by_bounds(back_button_bounds)
                time.sleep(SLEEP_SHORT)

        except Exception:
            self.logger.error("Error processing phone number: %s", format_exc())

        # Handle warning screen
        self._handle_warning_screen(retry_function=self.scroll_and_fetch_names)

    def resolve_better_results_popup(self, root=None):
        self.logger.debug("Resolving 'Better Results' popup")
        """Resolve 'Better Results' popup if present."""
        if root is None:
            root = self.emulator.get_node_tree()
        popup_title = xml_tree_cursor.find_node_with_attribute(
            root, "text", "Better Results"
        )
        if popup_title is None:
            return False
        cancel_button = xml_tree_cursor.find_node_with_attribute(root, "text", "CANCEL")
        cords = xml_tree_cursor.get_clickable_pixels_of_node(cancel_button)
        self.emulator.click_on_coordinates(cords[0], cords[1])
        time.sleep(SLEEP_SHORT)
        return True

    def resolve_block_popup(self, root=None):
        self.logger.debug("Resolving block popup")
        """Resolve block popup if present."""
        if root is None:
            root = self.emulator.get_node_tree()
        popup_title = xml_tree_cursor.find_node_with_attribute(root, "text", "البلوك")
        if popup_title is None:
            return False
        cancel_button = xml_tree_cursor.find_node_with_attribute(
            root, "resource-id", f"{self.package_name}:id/collapse_button"
        )
        cords = xml_tree_cursor.get_clickable_pixels_of_node(cancel_button)
        self.emulator.click_on_coordinates(cords[0], cords[1])
        time.sleep(SLEEP_SHORT)
        return True

    def resolve_permission_popup(self, root=None):
        self.logger.debug("Resolving permission popup")
        """Resolve permission popup by denying the permission if present."""
        if root is None:
            root = self.emulator.get_node_tree()
        dont_allow_button = xml_tree_cursor.find_node_with_attribute(
            root,
            "resource-id",
            "com.android.permissioncontroller:id/permission_deny_button",
        )
        if dont_allow_button is None:
            return False
        cords = xml_tree_cursor.get_clickable_pixels_of_node(dont_allow_button)
        self.emulator.click_on_coordinates(cords[0], cords[1])
        time.sleep(SLEEP_SHORT)
        return True

    def run_state_based(self):
        try:
            process_phone_number(self, self.reqeust.query, self.reqeust.id, 1, True)
        except Exception:
            self.logger.error("Exception in run_state_based: %s", format_exc())
            return {}
        finally:
            try:
                self.emulator.force_stop_app(self.get_package_name())
                self.emulator.empty_ram_with_adb()
            except Exception:
                pass
        if not self.done_automation:
            raise FailedAutomationException
        return self.results if self.results else {}

    @staticmethod
    def get_name() -> str:
        """Return the name identifier for this task."""
        return "numberbook_alkhalij"

    @staticmethod
    def get_package_name() -> str:
        """Return the package name associated with the task."""
        return "com.cuutt.gcc"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        """Check if the required app package is installed."""
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        """Return a list of supported regions as country codes."""
        return [
            CountryCodeEnum.SAUDI_ARABIA,
            CountryCodeEnum.UAE,
            CountryCodeEnum.BAHRAIN,
            CountryCodeEnum.KUWAIT,
            CountryCodeEnum.OMAN,
            CountryCodeEnum.QATAR,
            CountryCodeEnum.EGYPT,
            CountryCodeEnum.ALGERIA,
            CountryCodeEnum.IRAQ,
            CountryCodeEnum.JORDON,
            CountryCodeEnum.LEBANON,
            CountryCodeEnum.LIBYA,
            CountryCodeEnum.MOROCCO,
            CountryCodeEnum.PALASTINE,
            CountryCodeEnum.SUDAN,
            CountryCodeEnum.SYRIA,
            CountryCodeEnum.TUNISIA,
        ]

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        """Return a list of supported search types."""
        return [SearchTypeEnum.ByNumber, SearchTypeEnum.ByName]


def create_request(query: str, request_id: int, country_code: str) -> PhoneRequest:
    """Create and return a PhoneRequest object for name or number search."""
    search_type = SearchTypeEnum.ByNumber if query.isdigit() else SearchTypeEnum.ByName
    return PhoneRequest(
        id=request_id,
        search_type=search_type,
        country_code=country_code,
        query=query,
        status=RequestStatusEnum.NEW,
        created_at=datetime.now(),
        modified_at=datetime.now(),
    )


def process_phone_number(
    nba: Numberbook_Alkhalij,
    query: str,
    request_id: int,
    total_numbers: int,
    is_first: bool = False,
) -> None:
    nba.logger.info("Processing query %s (%d/%d)", query, request_id, total_numbers)
    print(f"\nProcessing {request_id}/{total_numbers}: {query}")

    nba.reqeust = create_request(query, request_id, nba.reqeust.country_code)

    if is_first:
        nba.cleared_app_data_init()
        nba.app_open_step()
        nba.change_current_region(nba.reqeust.country_code)

    try:
        if query.isdigit():
            nba.scroll_and_fetch_names()
        else:
            nba.scroll_and_fetch_numbers()
        nba.done_automation = True
        time.sleep(SLEEP_SHORT)
    except Exception:
        nba.logger.error("Error processing query: %s", format_exc())


def testing() -> None:
    print("Starting test sequence")
    """Run a basic test sequence on the Numberbook task with sample queries."""
    import os

    from Emulator import Emulator

    apk_path = "apks/com.cuutt.gcc/com.cuutt.gcc.apk"
    if not os.path.exists(apk_path):
        print(f"Error: APK not found at {apk_path}")
        return

    e = Emulator(1, "FA79V1A06540")

    test_queries = ["050975670", "loay", "ahmed", "mohammed", "abd", "arab", "omar"]

    try:
        print("\nChecking APK and initializing app...")
        initial_request = create_request(test_queries[0], 1, "964")
        nba = Numberbook_Alkhalij(e, initial_request)
        print("Starting test...")

        for i, query in enumerate(test_queries):
            process_phone_number(
                nba, query, i + 1, len(test_queries), is_first=(i == 0)
            )
            time.sleep(SLEEP_SHORT)

        print("\nTest completed successfully")
    except Exception:
        nba.logger.error("Test failed with error: %s", format_exc())
        print("Test failed with error:")
        print("Full traceback:")


if __name__ == "__main__":
    testing()
