from dotenv import load_dotenv
from utils import load_config
import json
import os

load_dotenv(".env")

NUMBER_OF_NAME_REQUESTS = 100

CONFIG = load_config()
DEBUGGING = CONFIG.get("DEBAGING", False)

# Constant for the search item resource id
SEARCH_ITEM_RESOURCE_ID = "com.callerid.wie:id/tvName"

QUERIES = json.loads(os.getenv("TEST_NAMES_QUERYS")) if os.getenv("TEST_NAMES_QUERYS") else []
DEVICE_NAME = os.getenv("DEVICE_NAME")
DEVICE_ID = os.getenv("DEVICE_ID")

MM_BACKEND_KEY = os.getenv("MM_BACKEND_KEY")
SERVER_URL = os.getenv("BACKEND_SERVER_URL", "http://localhost:5000")
DEVICE_SERIAL_ID = 1

GOOGLE_ACCOUNTS = json.loads(os.getenv("GOOGLE_ACCOUNTS")) if os.getenv("GOOGLE_ACCOUNTS") else []

USERNAME = None
EMAIL = None
PASSWORD = None

def set_google_accounts():
    global GOOGLE_ACCOUNTS
    GOOGLE_ACCOUNTS = json.loads(os.getenv("GOOGLE_ACCOUNTS")) if os.getenv("GOOGLE_ACCOUNTS") else []
    global USERNAME, EMAIL, PASSWORD

def get_google_accounts():
    global USERNAME, EMAIL, PASSWORD
    return USERNAME, EMAIL, PASSWORD

def handle_google_accounts():
    global USERNAME, EMAIL, PASSWORD
    
    if not GOOGLE_ACCOUNTS or len(GOOGLE_ACCOUNTS) == 0:
        USERNAME, EMAIL, PASSWORD = None, None, None
        return USERNAME, EMAIL, PASSWORD

    account = GOOGLE_ACCOUNTS.pop(0)
    if len(account) != 3:
        USERNAME, EMAIL, PASSWORD = None, None, None
        return USERNAME, EMAIL, PASSWORD
        
    USERNAME, EMAIL, PASSWORD = account
    return USERNAME, EMAIL, PASSWORD

