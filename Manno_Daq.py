from __future__ import annotations

import json
import time
from datetime import datetime
from typing import TYPE_CHECKING

import xml_tree_cursor
from error import log, setup_logger
from schemas import CountryCode<PERSON>num, PhoneRequest, RequestStatusEnum, SearchTypeEnum
from Task import AppStateBase, Task, UnknownAppState
from utils import format_xpath_attrs, load_config, remove_emojis, validate_word

if TYPE_CHECKING:
    from Emulator import Emulator

CONFIG = load_config()
DEBUGGING = False

# Constant for the search item resource id
SEARCH_ITEM_RESOURCE_ID = "menodag.arabdevs.android.app:id/search_item_title"


class MannoDaqAppState(AppStateBase):
    CHOOSE_LANGUAGE = "choose_language"
    SPAM_BLOCKING_POPUPS = "blocking_popups"
    INTRO_SCREEN = "intro_screen"
    BLOCING_AD_POPUP = "blocing_ad_popup"
    BETTER_RESULTS_MSG = "better_results_msg"
    SEARCH_INPUT_SCREEN = "search_input_screen"
    UNKNOWN = "unknown"


class Manno_Daq(Task):
    AppState = MannoDaqAppState
    XPATH_PATTERNS = {
        AppState.INTRO_SCREEN: ["//node[contains(@resource-id, 'introSkipButton')]"],
        AppState.SPAM_BLOCKING_POPUPS: [
            "//node[contains(@resource-id, 'collapse_button')]",
            "//node[contains(@text, 'لتعديل آسم أو إخفاء أو إبلاغ عن إساءة و الدعم')]",
        ],
        AppState.BLOCING_AD_POPUP: [
            "//node[contains(@resource-id, 'full_ad_closeButton')]",
            "//node[contains(@text, 'بلوك من هنا')]",
        ],
        AppState.CHOOSE_LANGUAGE: ["//node[contains(@resource-id, 'englishButton')]"],
        AppState.BETTER_RESULTS_MSG: [
            "//node[contains(@text, 'CANCEL')]",
            "//node[contains(@text, 'Better Results')]",
            "//node[contains(@text, 'When you grant contacts accessing')]",
        ],
        AppState.SEARCH_INPUT_SCREEN: [
            "//node[contains(@resource-id, 'search_tab_searchEditText')]",
            "//node[contains(@resource-id, 'search_tab_searchButtonImageView')]",
        ],
    }

    def __init__(
        self,
        emulator: Emulator,
        request: PhoneRequest,
    ) -> None:
        self.logger = setup_logger(
            f"Manno_Daq_{emulator.device_name}",
            f"logs/Manno_Daq_{emulator.device_name}.log",
        )
        super().__init__(
            emulator,
            request,
        )

        self.state_handlers = {
            self.AppState.SPAM_BLOCKING_POPUPS: self.press_back_handler,
            self.AppState.BLOCING_AD_POPUP: self.press_back_handler,
            self.AppState.INTRO_SCREEN: self.simple_state_handler,
            self.AppState.CHOOSE_LANGUAGE: self.simple_state_handler,
            self.AppState.BETTER_RESULTS_MSG: self.press_back_handler,
            self.AppState.SEARCH_INPUT_SCREEN: self.handle_search_screen,
            self.AppState.UNKNOWN: self.handle_unknown_state,
        }

    def handle_search_screen(self) -> bool:
        # TODO: pass the content as a parameter, not getting new one
        content = self.emulator.get_lxml_tree()
        search_type_xpath = format_xpath_attrs("search_tab_searchTypeSegment")
        view_xpath = "." + format_xpath_attrs(**{"class": "android.view.View"})
        search_type = content.xpath(search_type_xpath)[0]
        by_number_node, by_name_node = search_type.xpath(view_xpath)[-2:]
        search_field_xpath = (
            "//node[contains(@resource-id, 'search_tab_searchEditText')]"
        )
        search_button_xpath = (
            "//node[contains(@resource-id, 'search_tab_searchButtonImageView')]"
        )
        if self.reqeust.search_type == SearchTypeEnum.ByNumber:
            coordinates = self.emulator.get_element_click_coordinates(
                by_number_node, True
            )
        else:
            coordinates = self.emulator.get_element_click_coordinates(
                by_name_node, True
            )
        self.emulator.click_on_coordinates(coordinates[0], coordinates[1])
        time.sleep(0.5)

        search_bounds = self.emulator.get_bounds_by_xpath(content, search_field_xpath)
        self.emulator.click_element_by_bounds(search_bounds)
        time.sleep(1)
        self.emulator.clear_text_adb_keyboard()
        time.sleep(1)
        self.emulator.type_text_adb_keyboard(self.reqeust.query)
        time.sleep(1)
        search_button_bounds = self.emulator.get_bounds_by_xpath(
            content, search_button_xpath
        )
        self.emulator.click_element_by_bounds(search_button_bounds)
        time.sleep(30)  # wait 30 seconds to load results

        results = self.scroll_and_fetch_names(self.reqeust.query)
        self.results = results
        self.done_automation = True
        self.emulator.click_back_button()
        return True

    def scroll_and_fetch_names(self, phone: str) -> dict[str, int]:
        ui_dump = self.emulator.get_ui_hierarchy()
        node_tree = self.emulator.get_node_tree(ui_dump)
        text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
            node_tree, {"resource-id": f"{self.package_name}:id/search_item_title"}
        )
        search_item_resource_id = SEARCH_ITEM_RESOURCE_ID
        if self.reqeust.search_type == SearchTypeEnum.ByName:
            search_item_resource_id = f"{self.package_name}:id/search_item_desc"
        if not text_nodes_array:
            time.sleep(20)
            ui_dump = self.emulator.get_ui_hierarchy()
            node_tree = self.emulator.get_node_tree(ui_dump)
            text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                node_tree,
                {
                    "resource-id": search_item_resource_id,
                },
            )
            if not text_nodes_array:
                log(
                    self.emulator.main_logger,
                    "error",
                    f"Timeout while waiting for app to load names, skipping number {phone}",
                    True,
                    self.emulator.take_screenshot(),
                    ui_dump=self.emulator.xml_logger(ui_dump),
                )
                raise UnknownAppState()
        log(self.emulator.main_logger, "debug", "getting results")
        ui_dump = self.emulator.get_ui_hierarchy()
        node_tree = self.emulator.get_node_tree(ui_dump)
        count = 0
        all_results_of_number: dict[str, int] = {}
        self.true_loop_start_time = time.perf_counter()
        while True:
            text_nodes_array = xml_tree_cursor.find_nodes_with_attributes(
                node_tree, {"resource-id": search_item_resource_id}
            )
            count += 1
            for node in text_nodes_array:
                name = remove_emojis(node.attrib["text"])
                if "لتعديل أو مسح أو الإبلاغ عن إساءة".strip() in name:
                    is_showing_results = True
                if not validate_word(name):
                    continue
                all_results_of_number[name] = all_results_of_number.get(name, 0) + 1
            self.emulator.scroll_down(800)
            new_ui_dump = self.emulator.get_ui_hierarchy()
            if new_ui_dump == ui_dump:
                break
            ui_dump = new_ui_dump
            node_tree = self.emulator.get_node_tree(ui_dump)
            if time.perf_counter() - self.true_loop_start_time > 2700:
                raise Exception("Timed out while getting names")
        return all_results_of_number

    @staticmethod
    def get_name() -> str:
        return "manno_daq"

    @staticmethod
    def get_package_name() -> str:
        return "menodag.arabdevs.android.app"

    @classmethod
    def check_if_installed(cls, installed_apps: list[str]) -> bool:
        name = cls.get_package_name()
        return name in installed_apps

    @classmethod
    def supported_regions(cls) -> list[CountryCodeEnum]:
        return [CountryCodeEnum.KUWAIT]

    @classmethod
    def supported_search_types(cls) -> list[SearchTypeEnum]:
        return [SearchTypeEnum.ByNumber, SearchTypeEnum.ByName]


def testing() -> None:
    from Emulator import Emulator

    e = Emulator("KJ5n-OP", "192.168.1.113:5555")
    queries = ["67757762", "22970301", "61667530"]
    queries = ["عبيدة"]
    reqs = [
        PhoneRequest(
            id=i,
            search_type=SearchTypeEnum.ByName,
            country_code=CountryCodeEnum.KUWAIT.value,
            query=x,
            status=RequestStatusEnum.NEW,
            created_at=datetime.now(),
            modified_at=datetime.now(),
        )
        for i, x in enumerate(queries)
    ]
    for re in reqs:
        print("=================================")
        mno = Manno_Daq(e, re)
        mno.run_automation()
        with open(f"results_{re.id}.json", "w") as f:
            json.dump(mno.results, f, ensure_ascii=False)
            f.write("\n")


if __name__ == "__main__":
    testing()
