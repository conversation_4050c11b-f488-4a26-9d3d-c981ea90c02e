import base64
import os
import random
import shutil
import subprocess
import tempfile
import threading
import time
import xml.etree.ElementTree as ET
from datetime import datetime
from logging import Logger
from traceback import format_exc
from typing import List, Sequence, Type

# Remove all EasyOCR and PIL dependencies
import phonenumbers
from lxml import etree
from phonenumbers import carrier, geocoder, timezone

from error import log, setup_logger
from get_contact import GetContact
from hello_caller.hello_caller import HelloCaller
from Manno_Daq import Manno_Daq
from mannodaq_kw import MannodaqKw
from Numberbook_Alkhalij import Numberbook_Alkhalij
from realcaller import RealCaller
from Task import Task
from utils import process_and_upload_backup
from xml_tree_cursor import get_clickable_pixels_of_node

# Remove all EasyOCR and PIL dependencies


DEBUGGING = True


CHANNELS: Sequence[Type[Task]] = [
    GetContact,
    HelloCaller,
    RealCaller,
    Numberbook_Alkhalij,
    MannodaqKw,
    Manno_Daq,
]

ALL_CHANNELS_WITHOUT_EXCLUDE: Sequence[Type[Task]] = [
    <PERSON><PERSON><PERSON><PERSON>,
    HelloCaller,
    <PERSON><PERSON><PERSON>r,
    Numberbook_Alkhalij,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>_<PERSON>q,
]


class NotFoundException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class NotClickableException(Exception):
    def __init__(self, *args: object) -> None:
        super().__init__(*args)


class Emulator:
    # Class-level semaphore to limit concurrent UI hierarchy operations
    _ui_hierarchy_semaphore = threading.Semaphore(
        int(os.getenv("UI_HIERARCHY_SEMAPHORE_LIMIT", "30"))
    )

    def __init__(
        self,
        device_id,
        device_name: str,
        slow_multiplier=1,
    ):
        self.slow_multiplier = slow_multiplier
        self.full_name_writable = f"{device_id}_{device_name.replace(':', '_')}"
        xml_log_dir = f"xml_log/{self.full_name_writable}"
        if not os.path.exists(xml_log_dir):
            os.makedirs(xml_log_dir)
        self.device_id = device_id
        self._model = None
        self.running = True
        self.device_name = device_name
        self.emulator = "emulator" in device_name or ":" in device_name
        self.last_used = datetime.now()
        self.lock = threading.RLock()
        self.main_logger = setup_logger(
            f"emulator_{device_name}_{device_id}", f"logs/{self.full_name_writable}.log"
        )
        self.logger = setup_logger(
            f"emulator_{device_name}_{device_id}_logger",
            f"logs/{self.full_name_writable}_logger.log",
        )
        self.name_and_model = self.get_full_name_and_model
        self.width, self.height = self.get_screen_size()

    def __str__(self) -> str:
        return f"Emulator({self.device_name}, id={self.device_id})"

    def xml_logger(self, ui_dump: str, logger: Logger | None = None) -> str:
        """Logs the given UI dump into a file and returns the file name."""
        name = ""
        if logger:
            name = logger.name + "_"

        file_name = f"xml_{name}{int(time.perf_counter() // 1)}.xml"
        path = os.path.join("xml_log", self.full_name_writable, file_name)
        if not isinstance(ui_dump, str):
            ui_dump = etree.tostring(ui_dump, encoding="utf-8", method="xml").decode(
                "utf-8"
            )
        with open(path, "w", encoding="utf-8") as f:
            f.write(ui_dump)
        if logger is not None:
            log(logger, "debug", f"logged ui dump to {file_name}")
        return file_name

    def mark_as_used(self):
        """Mark this emulator as recently used."""
        with self.lock:
            self.last_used = datetime.now()

    def click_on_node(self, node):
        self.click_on_coordinates(*get_clickable_pixels_of_node(node))

    def click_element_by_bounds(self, bounds: str) -> bool:
        """Click on an element using its bounds [x1,y1][x2,y2]."""
        try:
            coords = bounds.strip("[]").replace("][", ",").split(",")
            x1, y1, x2, y2 = map(int, coords)
            center_x = (x1 + x2) // 2 + random.randint(-5, 5)
            center_y = (y1 + y2) // 2 + random.randint(-5, 5)
            self.exec_android_sh_command(f"shell input tap {center_x} {center_y}")
            self.main_logger.debug(f"Tapped at coordinates: ({center_x}, {center_y})")
            return True
        except Exception as e:
            self.main_logger.error(f"Failed to execute tap command: {e}")
            return False

    def find_focused_editable_element(self, tree=None):
        """Find a focused element or fall back to an editable element if none found.

        Args:
            tree: Optional lxml tree. If None, gets the current UI hierarchy.

        Returns:
            tuple: (element, is_focused) where element is the lxml element and
                  is_focused is a boolean indicating if it was a focused element.

        Raises:
            NotFoundException: If no suitable element could be found.
        """
        if tree is None:
            tree = self.get_lxml_tree()

        # Try to find focused element first
        focused_elements = tree.xpath("//*[@focused='true']")
        if focused_elements:
            return focused_elements[0], True

        # Fall back to editable elements
        self.main_logger.debug(
            "No focused element found, trying to find editable text fields..."
        )
        editable_elements = tree.xpath(
            "//*[@editable='true' or @class='android.widget.EditText']"
        )

        if editable_elements:
            return editable_elements[0], False

        self.main_logger.warning("No focused or editable element found")
        raise NotFoundException("No focused or editable element found")

    def get_element_click_coordinates(self, element, click_center=False):
        """Get the coordinates to click based on an element's bounds.

        Args:
            element: The XML element with bounds attribute
            click_center: If True, click at center; if False, click at left edge

        Returns:
            tuple: (x, y) coordinates to click

        Raises:
            NotClickableException: If the element has no bounds
        """
        bounds = element.get("bounds")
        if not bounds:
            self.main_logger.error("Element has no bounds")
            raise NotClickableException("Element has no bounds for click operation")

        try:
            coords = bounds.strip("[]").replace("][", ",").split(",")
            x1, y1, x2, y2 = map(int, coords)

            if click_center:
                # Use center of element
                x = (x1 + x2) // 2
                y = (y1 + y2) // 2
                position_type = "center"
            else:
                # Use leftmost part with small offset from edge (better for text selection)
                x = x1 + 10  # Small offset from the left edge
                y = (y1 + y2) // 2  # Vertical center
                position_type = "leftmost"

            self.main_logger.debug(f"Using {position_type} position: ({x}, {y})")
            return x, y, position_type

        except Exception as e:
            self.main_logger.error(f"Failed to parse element bounds: {bounds}")
            raise NotClickableException(f"Invalid bounds format: {bounds}") from e

    def double_click_focused_node(self, click_center=False):
        """Performs a double click on the currently focused node.

        Args:
            click_center (bool): If True, clicks at the center of the node instead of left edge.
                                Default is False (click at left edge).

        Raises:
            NotFoundException: If no suitable element is found.
            NotClickableException: If the element has no valid bounds.
            Exception: For any other errors during the double-click operation.

        Note:
            This function is legacy. clear_text_adb_keyboard is now recommended for clearing text.
        """
        try:
            # Find focused or editable element
            element, is_focused = self.find_focused_editable_element()

            # If we found an editable element but it's not focused, click it first
            if not is_focused:
                x, y, _ = self.get_element_click_coordinates(element, click_center)
                self.click_on_coordinates(x, y)
                time.sleep(2)  # Wait for focus to change

                # Try again to find focused element
                element, _ = self.find_focused_editable_element()

            # Get click coordinates
            x, y, position_type = self.get_element_click_coordinates(
                element, click_center
            )

            # Perform double click
            self.exec_android_sh_command(f"shell input tap {x} {y}")
            time.sleep(0.05)
            self.exec_android_sh_command(f"shell input tap {x} {y}")

            self.main_logger.debug(
                f"Double-clicked at {position_type} position: ({x}, {y})"
            )
            return True

        except Exception as e:
            self.main_logger.error(f"Failed to execute double-click: {e}")
            raise

    def long_click_focused_node(self, click_center=False):
        """Performs a long click on the currently focused node.

        Args:
            click_center (bool): If True, clicks at the center of the node instead of left edge.
                                Default is False (click at left edge).

        Note:
            This function is legacy. clear_text_adb_keyboard is now recommended for clearing text.
        """
        try:
            # Find focused or editable element
            element, is_focused = self.find_focused_editable_element()

            # If we found an editable element but it's not focused, click it first
            if not is_focused:
                x, y, _ = self.get_element_click_coordinates(element, click_center)
                self.click_on_coordinates(x, y)
                time.sleep(2)  # Wait for focus to change

                # Try again to find focused element
                element, _ = self.find_focused_editable_element()

            # Get click coordinates
            x, y, position_type = self.get_element_click_coordinates(
                element, click_center
            )

            self.exec_android_sh_command(f"shell input swipe {x} {y} {x} {y} 1000")

            self.main_logger.debug(
                f"Long-clicked at {position_type} position: ({x}, {y})"
            )
            return True

        except Exception as e:
            self.main_logger.error(f"Failed to execute long-click: {e}")
            raise

    def verify_internet_connection(self):
        result = self.exec_android_sh_command("shell ping -c 4 *******", True, 20)
        for line in result.splitlines():
            if "packets transmitted" in line:
                transmitted = int(line.split(", ")[1].split(" ")[0])
                return transmitted != 0
        return None

    def exec_android_sh_command(
        self,
        command: str,
        read_output: bool = False,
        timeout: int = 30,
        direct_exec=False,
    ):
        """Executes an Android shell command via adb."""
        prefix = (
            ""
            if command.startswith("adb")
            else (f"adb -s {self.device_name} " if self.device_name else "adb ")
        )
        full_command = f"{prefix}{command}"
        if direct_exec:
            cmd = full_command
        else:
            cmd = full_command.split()
        if DEBUGGING:
            log(
                self.main_logger,
                "debug",
                f"running adb command\n{full_command}",
                send_on_telegram=False,
            )
        if read_output:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding="utf-8",
                timeout=timeout,
                shell=direct_exec,
            )
            return result.stdout
        else:
            subprocess.run(cmd, timeout=timeout, shell=direct_exec)

    def change_to_root(self):
        self.exec_android_sh_command("root")

    def change_to_user(self):
        self.exec_android_sh_command("unroot")

    def change_sim_state(self, phone_number=None, accept_default=False):
        """
        Changes the SIM state properties based on a given phone number.
        Uses the phonenumbers library to extract country and carrier information.

        Args:
            phone_number (str, optional): Phone number in international format (e.g. "+573171337870").
                                         If None, defaults to Colombia Claro number.
        """
        # Default to existing number if none provided
        if phone_number is None:
            phone_number = "+573171337870"

        # Parse the phone number using the phonenumbers library
        try:
            parsed_number = phonenumbers.parse(phone_number)

            # Get country information
            country_code = phonenumbers.region_code_for_number(parsed_number)
            country_code = country_code.lower() if country_code else "us"

            # Get carrier information if available
            carrier_name = carrier.name_for_number(parsed_number, "en")
            if not carrier_name:
                # Try to get a meaningful name based on country
                geo_name = geocoder.description_for_number(parsed_number, "en") or ""
                carrier_name = f"Mobile {geo_name}".strip()
                if not carrier_name:
                    carrier_name = f"Mobile {country_code.upper()}"

            # Try to get Mobile Country Code (MCC) and Mobile Network Code (MNC)
            # If not available, create default based on country code
            country_info = phonenumbers.PhoneMetadata._region_metadata.get(
                country_code.upper()
            )
            if country_info and hasattr(country_info, "mobile_country_code"):
                mcc = country_info.mobile_country_code
            else:
                # Fallback: try to get from timezone data
                regions = timezone.time_zones_for_number(parsed_number)
                region = next(iter(regions), None)
                # Simple mapping for common countries (incomplete)
                mcc_map = {
                    "us": "310",
                    "ca": "302",
                    "gb": "234",
                    "de": "262",
                    "fr": "208",
                    "it": "222",
                    "es": "214",
                    "co": "732",
                    "mx": "334",
                    "br": "724",
                    "ar": "722",
                    "au": "505",
                }
                mcc = mcc_map.get(country_code.lower(), "001")

            # Default MNC (network code)
            mnc = "001"
            numeric_code = f"{mcc}{mnc}"

        except Exception as e:
            if not accept_default:
                raise
            log(self.main_logger, "error", f"Error parsing phone number: {e}")
            # Fall back to defaults
            phone_number = "+573171337870"
            country_code = "co"
            carrier_name = "Mobile"
            numeric_code = "732101"

        # Replace spaces with escaped spaces for shell command
        carrier_name_escaped = carrier_name.replace(" ", "\\ ")

        # self.change_to_root()
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.operator.alpha {carrier_name_escaped}'\"",
            direct_exec=True,
        )
        command = f"shell \"su -c 'setprop gsm.operator.iso-country {country_code}'\""
        self.exec_android_sh_command(
            command,
            direct_exec=True,
        )
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.operator.numeric {numeric_code}'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            "shell \"su -c 'setprop gsm.state READY'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.phonenumber {phone_number}'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            "shell \"su -c 'setprop gsm.operator.isroaming false'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            "shell \"su -c 'setprop gsm.network.type LTE'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            "shell \"su -c 'setprop gsm.sim.operator.isroaming false'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            "shell \"su -c 'setprop gsm.sim.network.type LTE'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.sim.operator.alpha {carrier_name_escaped}'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.sim.operator.iso-country {country_code}'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.sim.operator.numeric {numeric_code}'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            "shell \"su -c 'setprop gsm.sim.state READY'\"",
            direct_exec=True,
        )
        self.exec_android_sh_command(
            f"shell \"su -c 'setprop gsm.sim.phonenumber {phone_number}'\"",
            direct_exec=True,
        )
        # self.change_to_user()

        log(
            self.main_logger,
            "debug",
            f"Changed SIM state to {carrier_name} ({country_code}) with number {phone_number}",
        )

    def test_connectivity(self):
        try:
            self.click_home_button()
            self.get_node_tree()
            return self.get_own_state(), 0
        except Exception as e:
            return False, e

    def get_device_model(self) -> str:
        parts = [
            self.exec_android_sh_command(
                "shell getprop ro.product.vendor.manufacturer", True
            ).strip(),
            self.exec_android_sh_command("shell getprop ro.product.name", True).strip(),
            self.exec_android_sh_command(
                "shell getprop ro.product.model", True
            ).strip(),
            self.exec_android_sh_command(
                "shell getprop ro.product.device", True
            ).strip(),
        ]
        return "_".join(parts)

    @property
    def get_full_name_and_model(self) -> str:
        try:
            return self.get_device_model()
        except Exception:
            return self.device_name + "_(unknown model)"

    def get_screen_size(self):
        try:
            result = self.exec_android_sh_command("shell wm size", True).strip()
            if result:
                self.width, self.height = result.split()[-1].split("x")
                self.width = int(self.width)
                self.height = int(self.height)
                return self.width, self.height
        except Exception as e:
            self.main_logger.error(f"Failed to get screen size: {e}")
        return 800, 1280

    def click_tab(self):
        self.exec_android_sh_command("shell input keyevent 61")

    def change_screen_timeout(self, milliseconds: int = 1800000):
        self.exec_android_sh_command(
            f"shell settings put system screen_off_timeout {milliseconds}"
        )

    def click_enter(self):
        self.exec_android_sh_command("shell input keyevent 66")

    def install_app(self, relative_path: str):
        return self.exec_android_sh_command(
            f"install {relative_path}",
            read_output=True,
            timeout=90 * self.slow_multiplier,
        )

    def install_multi_app(self, relative_paths: list, timeout: int = None):
        paths = (
            " ".join(relative_paths)
            if isinstance(relative_paths, list)
            else relative_paths
        )
        effective_timeout = 90 * self.slow_multiplier if timeout is None else timeout
        self.exec_android_sh_command(
            f"install-multiple {paths}", timeout=effective_timeout
        )

    def click_done_button_indirect(self):
        self.click_tab()
        self.click_enter()

    def set_portrait_orientation(self):
        self.exec_android_sh_command("shell settings put system user_rotation 0")
        time.sleep(0.7)

    def disable_auto_rotation(self):
        self.exec_android_sh_command(
            "shell settings put system accelerometer_rotation 0"
        )

    def click_on_coordinates(self, first: int, second: int):
        self.exec_android_sh_command(f"shell input tap {first} {second}")

    def click_back_button(self):
        self.exec_android_sh_command("shell input keyevent 4")
        time.sleep(1)

    def click_home_button(self):
        self.exec_android_sh_command("shell input keyevent KEYCODE_HOME")
        time.sleep(4 * self.slow_multiplier)

    def scroll_until(self, assertion_function, max_scrolls: int = 5):
        previous_hierarchy = ""
        scrolls = 0
        while True:
            hierarchy = self.get_ui_hierarchy()
            if hierarchy == previous_hierarchy:
                return None
            tree = self.get_node_tree(hierarchy)
            result = assertion_function(tree)
            if result is not None:
                return result
            if scrolls >= max_scrolls:
                return None
            previous_hierarchy = hierarchy
            self.scroll_down()
            scrolls += 1

    # TODO: Check this function again
    # def do_and_scroll(self, do_function, max_scrolls: int = 20):
    #     previous_hierarchy = ""
    #     scrolls = 0
    #     while True:
    #         hierarchy = self.get_ui_hierarchy()
    #         if hierarchy == previous_hierarchy:
    #             return None
    #         tree = self.get_node_tree(hierarchy)
    #         result = do_function(tree)
    #         if result is not None:
    #             return result
    #         if scrolls >= max_scrolls:
    #             return None
    #         previous_hierarchy = hierarchy
    #         self.scroll_down()
    #         scrolls += 1

    def scroller(self, max_scrolls: int = 5):
        previous_hierarchy = ""
        scrolls = 0
        while True:
            hierarchy = self.get_ui_hierarchy()
            if hierarchy == previous_hierarchy:
                return
            tree = self.get_node_tree(hierarchy)
            yield tree
            if scrolls >= max_scrolls:
                return
            previous_hierarchy = hierarchy
            self.scroll_down()
            scrolls += 1

    def clear_phone_number(self):
        for _ in range(5):
            self.exec_android_sh_command("shell input keyevent 67")
            self.exec_android_sh_command("shell input keyevent 112")

    def clear_all_text(self):
        self.exec_android_sh_command("shell input keycombination 113 29")
        self.exec_android_sh_command("shell input keyevent 67")

    def press_backspace(self):
        self.exec_android_sh_command("shell input keyevent 67")
        time.sleep(0.1)

    def clear_all_text_compatible(
        self, characters_to_delete: int = 8, use_arrow_down: bool = False
    ):
        if use_arrow_down:
            self.exec_android_sh_command("shell input keyevent 20")
        else:
            self.exec_android_sh_command("shell input keyevent 123")
        for _ in range(characters_to_delete):
            self.press_backspace()

    def clear_text_adb_keyboard(self):
        """
        Clears text in the currently focused field using ADBKeyboard's special intent.
        Requires ADBKeyboard to be installed.
        """
        # Set ADBKeyboard as current input method
        self.exec_android_sh_command(
            "shell ime set com.android.adbkeyboard/.AdbIME", True
        )
        time.sleep(1)
        # Send clear text broadcast
        self.exec_android_sh_command("shell am broadcast -a ADB_CLEAR_TEXT", True)
        time.sleep(0.5)
        log(self.main_logger, "debug", "cleared text using ADBKeyboard")

    def type_text_adb_keyboard(self, text: str):
        """
        Types text using ADBKeyboard's special intent, supporting UTF-8 characters including Arabic.
        Requires ADBKeyboard to be installed.

        Args:
            text: The text to type, can include Arabic and other UTF-8 characters
        """
        # Set ADBKeyboard as current input method
        charsb64 = str(base64.b64encode(text.encode("utf-8")))[1:]
        self.exec_android_sh_command(
            "shell ime set com.android.adbkeyboard/.AdbIME", True
        )
        time.sleep(1)
        self.exec_android_sh_command(
            f"shell am broadcast -a ADB_INPUT_B64 --es msg {charsb64}", True
        )
        time.sleep(0.5)
        log(self.main_logger, "debug", f"typed text using ADBKeyboard: {text}")

    def get_own_state(self):
        state = self.exec_android_sh_command("get-state", True)
        if not state or "not found" in state or "error" in state:
            log(
                self.main_logger,
                "error",
                f"device/emulator inoperative : {self.device_name} - {self.model}",
            )
            self.running = False
            return False
        self.running = True
        return True

    def _trim_ui_dump(self, ui_dump: str) -> str:
        """Helper to trim the UI dump to a well-formed XML fragment."""
        trimmed = ui_dump[ui_dump.rfind("<?xml") :]
        return trimmed.split("</hierarchy>")[0] + "</hierarchy>"

    def get_node_tree(self, ui_dump: str | None = None):
        if ui_dump is None:
            ui_dump = self.get_ui_hierarchy()
        trimmed_ui_dump = self._trim_ui_dump(ui_dump)
        try:
            node_tree = ET.fromstring(trimmed_ui_dump)
        except Exception:
            log(
                self.main_logger,
                "warning",
                f"couldn't extract xml from dump\n{ui_dump[:50]}",
                ui_dump_name=self.xml_logger(ui_dump),
            )
            raise
        return node_tree

    def get_lxml_tree(self, ui_dump: str | None = None) -> etree.ElementBase:
        if ui_dump is None:
            ui_dump = self.get_ui_hierarchy()
        trimmed_ui_dump = self._trim_ui_dump(ui_dump).encode("utf-8")
        try:
            node_tree = etree.XML(trimmed_ui_dump)
        except Exception:
            log(
                self.main_logger,
                "warning",
                f"couldn't extract xml from dump\n{ui_dump[:50]}",
                ui_dump_name=self.xml_logger(ui_dump),
            )
            raise
        return node_tree

    def find_clickable(
        self, node: etree.ElementBase, ignore_logs: bool = False
    ) -> str | None:
        """
        Traverses up the node hierarchy to find a clickable element.
        Returns the bounds if found.
        """
        # Check if the current node is clickable
        if node.get("clickable") == "true":
            bounds = node.get("bounds")
            self.logger.debug(f"Clickable found on current node with bounds: {bounds}")
            return bounds

        # Check if any sibling nodes are clickable
        parent = node.getparent()
        if parent is not None:
            # Get all children of the parent
            siblings = parent.getchildren()
            try:
                current_index = siblings.index(node)
            except ValueError:
                self.logger.error("Current node not found among parent's children.")
                current_index = -1

            # Check following siblings for a clickable element
            for sibling in siblings[current_index + 1 :]:
                if sibling.get("clickable") == "true":
                    bounds = sibling.get("bounds")
                    self.logger.debug(
                        f"Clickable found on following sibling with bounds: {bounds}"
                    )
                    return bounds

            # Check preceding siblings for a clickable element
            for sibling in reversed(siblings[:current_index]):
                if sibling.get("clickable") == "true":
                    bounds = sibling.get("bounds")
                    self.logger.debug(
                        f"Clickable found on preceding sibling with bounds: {bounds}"
                    )
                    return bounds

            # Check if the parent itself is clickable
            if parent.get("clickable") == "true":
                bounds = parent.get("bounds")
                self.logger.debug(
                    f"Clickable found on parent node with bounds: {bounds}"
                )
                return bounds

            # Recursively search up the hierarchy
            recursive_bounds = self.find_clickable(parent)
            if recursive_bounds:
                return recursive_bounds
        if not ignore_logs:
            self.logger.error(
                "Could not find a clickable element starting from the provided node."
            )
        return None

    def click_by_xpath_direct(self, content: etree.ElementBase, xpath: str):
        """
        Clicks an element directly by its xpath without checking if it's clickable.
        Just gets the bounds and clicks whatever it is.
        """
        elements = content.xpath(xpath)
        if not elements:
            raise NotFoundException

        # Get the bounds of the first matching element
        bounds = elements[0].get("bounds")
        if not bounds:
            self.main_logger.error("Element found but has no bounds")
            raise NotClickableException
        self.click_element_by_bounds(bounds)

    def click_by_xpath(self, content: etree.ElementBase, xpath: str):
        bounds = self.get_bounds_by_xpath(content, xpath)
        self.click_element_by_bounds(bounds)

    def get_bounds_by_xpath(
        self,
        content: etree.ElementBase,
        xpath: str,
        raise_exception: bool = True,
        ignore_logs: bool = False,
    ) -> str | None:
        elements = content.xpath(xpath)
        if not elements:
            self.logger.error(f"No elements found for the XPath: {xpath}")
            if raise_exception:
                raise NotFoundException(f"No elements found for the XPath: {xpath}")
            else:
                return None

        for node in elements:
            bounds = self.find_clickable(node, ignore_logs=ignore_logs)
            if bounds:
                return bounds

        if not ignore_logs:
            self.logger.error(
                f"None of the elements found using XPath '{xpath}' are clickable."
            )
        if raise_exception:
            raise NotClickableException(
                f"None of the elements found using XPath '{xpath}' are clickable."
            )
        else:
            return None

    def direct_click_on_node(self, node: etree.ElementBase):
        bounds = node.get("bounds")
        if not bounds:
            raise NotClickableException
        self.click_element_by_bounds(bounds)

    def get_path_of_node(self, node: etree.ElementBase):
        return etree.ElementTree(node).getpath(node)

    @property
    def model(self):
        if self._model is None:
            self._model = self.exec_android_sh_command(
                "shell getprop ro.product.model", True
            )
            if "adb" in self._model:
                self._model = ""
        return self._model

    def get_ui_hierarchy(self):
        retries = 0
        while retries < 3:
            with self._ui_hierarchy_semaphore:
                ui_dump = self.exec_android_sh_command(
                    "exec-out uiautomator dump /dev/tty", read_output=True, timeout=60
                )
            xml_text = ui_dump.split("\n", 1)[1] if "\n" in ui_dump else ""
            log(
                self.main_logger,
                "debug",
                "getting ui dump",
                ui_dump_name=self.xml_logger(xml_text),
            )
            if 'CANNOT LINK EXECUTABLE "app_process"' in ui_dump:
                log(
                    self.main_logger,
                    "error",
                    "CANNOT LINK EXECUTABLE 'app_process' when trying to get ui dump",
                    True,
                    self.take_screenshot(),
                )
                raise Exception("dump failed")
            if "ERROR: null root node returned by UiTestAutomation" not in xml_text:
                return ui_dump
            retries += 1
        raise Exception(xml_text[:50])

    def take_screenshot(self) -> bytes:
        remote_path = "/data/local/tmp/screen.png"

        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            local_path = tmp_file.name

        try:
            subprocess.run(
                [
                    "adb",
                    "-s",
                    self.device_name,
                    "shell",
                    "screencap",
                    "-p",
                    remote_path,
                ],
                check=True,
                timeout=10,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            subprocess.run(
                ["adb", "-s", self.device_name, "pull", remote_path, local_path],
                check=True,
                timeout=10,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            subprocess.run(
                ["adb", "-s", self.device_name, "shell", "rm", remote_path],
                check=True,
                timeout=10,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            with open(local_path, "rb") as f:
                return f.read()

        except subprocess.CalledProcessError as e:
            self.logger.error(f"ADB command failed: {e.stderr.decode().strip()}")
            raise

        finally:
            if os.path.exists(local_path):
                os.remove(local_path)

    def capture_and_find_text(self, text_to_find: str) -> bool:
        """
        Capture a screenshot immediately and check for text using a simple method.
        This is optimized to catch fast-disappearing toast messages.

        """
        try:
            search_text = text_to_find.lower()

            try:
                # Use dumpsys to find active toasts
                toast_check = self.exec_android_sh_command(
                    "shell dumpsys notification | grep -A 3 Toast", True, timeout=2
                )
                if search_text in toast_check.lower():
                    return True

                # Check if toast_check contains any limit-related words
                limit_keywords = ["maximum", "daily", "limit", "reached", "search"]
                matches = sum(1 for kw in limit_keywords if kw in toast_check.lower())
                if matches >= 2:
                    return True
            except Exception as e:
                self.logger.error(f"Toast check failed: {str(e)}")

            #  Check window for dialogs
            try:
                # Use dumpsys window to find dialogs and popups
                window_info = self.exec_android_sh_command(
                    "shell dumpsys window windows | grep -A 3 -i 'Window #'",
                    True,
                    timeout=2,
                )
                if search_text in window_info.lower():
                    return True

                # Check specifically for phrases related to daily limit
                if "maximum" in window_info.lower() and (
                    "daily" in window_info.lower()
                    or "limit" in window_info.lower()
                    or "reached" in window_info.lower()
                ):
                    return True
            except Exception as e:
                self.logger.error(f"Window check failed: {str(e)}")

            try:
                # Look for message dialogs in current activity
                activity_info = self.exec_android_sh_command(
                    "shell dumpsys activity top", True, timeout=2
                )
                if search_text in activity_info.lower():
                    self.logger.debug(f"Found '{search_text}' in current activity")
                    return True

                # Check for TextView elements with limit message
                if (
                    "maximum" in activity_info.lower()
                    and "textview" in activity_info.lower()
                ):
                    self.logger.debug(
                        "Found maximum related TextView in current activity"
                    )
                    return True
            except Exception as e:
                self.logger.error(f"Activity check failed: {str(e)}")

            try:
                # Logcat often captures toast messages
                logcat_output = self.exec_android_sh_command(
                    "shell logcat -d -b main | grep -i toast", True, timeout=2
                )
                if search_text in logcat_output.lower():
                    self.logger.debug(f"Found '{search_text}' in logcat")
                    return True

                # Check for specific keywords in recent logs
                if "maximum" in logcat_output.lower() and (
                    "daily" in logcat_output.lower()
                    or "limit" in logcat_output.lower()
                    or "reached" in logcat_output.lower()
                ):
                    self.logger.debug("Found maximum daily limit related words in logs")
                    return True
            except Exception as e:
                self.logger.error(f"Logcat check failed: {str(e)}")

            return False

        except Exception as e:
            log(self.main_logger, "warning", f"Error in text detection: {str(e)}")
            return False

    def force_stop_app(self, package_name: str):
        print("force stopping")
        self.exec_android_sh_command(f"shell am force-stop {package_name}")
        time.sleep(1)

    def clear_data(self, package_name: str):
        self.exec_android_sh_command(f"shell pm clear {package_name}")
        time.sleep(2)
        log(self.main_logger, "debug", "cleared app")

    def get_requested_permissions(self, package_name: str):
        res = self.exec_android_sh_command(
            f"adb shell dumpsys package {package_name}", True
        )
        perms = []
        for line in res.splitlines():
            if "permission" in line and "granted=false" in line:
                perms.append(line.split(":")[0].strip())
        return perms

    def enable_permission(self, package_name: str, permission: str):
        return self.exec_android_sh_command(
            f"adb shell pm grant {package_name} {permission}"
        )

    def type_text(self, text: str):
        self.exec_android_sh_command(f"shell input text {text}")

    def open_app(self, package_name: str):
        self.open_phone()  # to make sure it's on

        # Dynamically resolve the main activity
        activity_info = (
            self.exec_android_sh_command(
                f"shell cmd package resolve-activity --brief {package_name}",
                read_output=True,
            )
            .strip()
            .splitlines()
        )

        resolved_activity = None
        for line in activity_info:
            if line.startswith(package_name):
                resolved_activity = line.strip()
                break

        if not resolved_activity:
            log(
                self.main_logger,
                "error",
                f"Could not resolve activity for {package_name}",
            )
            raise RuntimeError(f"Could not resolve activity for {package_name}")

        output = self.exec_android_sh_command(
            f"shell am start -n {resolved_activity}",
            read_output=True,
        )
        log(self.main_logger, "debug", f"open app {package_name}", output=output)

        time.sleep(6 * self.slow_multiplier)
        self.disable_auto_rotation()
        self.set_portrait_orientation()
        time.sleep(8 * self.slow_multiplier)
        log(self.main_logger, "debug", "opened app")

    def enable_stay_awake(self):
        self.exec_android_sh_command(
            "shell settings put global stay_on_while_plugged_in 3"
        )

    def disable_stay_awake(self):
        self.exec_android_sh_command(
            "shell settings put global stay_on_while_plugged_in 3"
        )

    def scroll_down(self, distance=1000, duration=None):
        """
        Scroll down with adjustable parameters.

        Args:
            distance: The vertical distance to scroll (default: 1000)
            duration: Optional scroll duration in ms (e.g., '300' for 300ms)
        """
        start_y = self.height // 2
        start_x = self.width // 2
        end_y = start_y - distance

        command = f"shell input swipe {start_x} {start_y} {start_x} {end_y}"
        if duration is not None:
            command += f" {duration}"

        self.exec_android_sh_command(command)
        log(self.main_logger, "debug", f"scrolled down (distance: {distance})")

    def scroll_up(self, distance=1000, duration=None):
        """
        Scroll up with adjustable parameters.

        Args:
            distance: The vertical distance to scroll (default: 1000)
            duration: Optional scroll duration in ms (e.g., '300' for 300ms)
        """
        start_y = self.height // 2
        start_x = self.width // 2
        end_y = start_y + distance

        command = f"shell input swipe {start_x} {start_y} {start_x} {end_y}"
        if duration is not None:
            command += f" {duration}"

        self.exec_android_sh_command(command)
        log(self.main_logger, "debug", f"scrolled up (distance: {distance})")

    def is_screen_on(self):
        screen_state = self.exec_android_sh_command(
            'shell dumpsys display | grep "mScreenState"', True
        )
        return any(
            line.split("=")[1].upper().strip() == "ON"
            for line in screen_state.splitlines()
            if "=" in line
        )

    def open_phone(self):
        if self.emulator:
            return
        if not self.is_screen_on():
            self.exec_android_sh_command("shell input keyevent 26")
        time.sleep(0.5)
        self.exec_android_sh_command("shell input keyevent 82")
        time.sleep(1)
        self.change_screen_timeout()

    def start_device(self):
        log(self.main_logger, "INFO", "Starting the device....")
        if not self.emulator:
            self.open_phone()
        for _ in range(2):
            self.click_home_button()

    def is_device_rooted(self) -> bool:
        """
        Check if the Android device is rooted.

        Returns:
            bool: True if the device is rooted, False otherwise.
        """
        log(self.main_logger, "debug", "Checking if device is rooted...")

        # Method 3: Check for common root apps
        root_packages = [
            "com.noshufou.android.su",
            "com.koushikdutta.superuser",
            "eu.chainfire.supersu",
            "com.topjohnwu.magisk",
            "io.github.huskydg.magisk",
        ]

        installed_packages = self.exec_android_sh_command(
            "shell pm list packages", True
        )
        for package in root_packages:
            if package in installed_packages:
                log(self.main_logger, "info", f"Found root app: {package}")
                return True

        log(self.main_logger, "info", "Device does not appear to be rooted")
        return False

    def get_third_party_apps(self):
        apps = [
            line.split(":")[1]
            for line in self.exec_android_sh_command(
                "shell pm list packages -3", True
            ).splitlines()
        ]
        return apps

    def get_foreground_app_beta(self):
        raw_result = self.exec_android_sh_command(
            "shell dumpsys activity activities", True
        )
        try:
            info = {}
            for line in raw_result.splitlines():
                if (
                    "topDisplayFocusedStack=Task" in line
                    or "topDisplayFocusedRootTask=Task" in line
                ):
                    result = line.split("{")[1].split(" ")
                    for text in result:
                        if "=" in text:
                            key, value = (
                                text.split("=")[0],
                                text.split("=")[1].replace("}", ""),
                            )
                            info[key] = value
                    if info.get("type") == "home":
                        return "HOME"
                    elif info.get("type") == "standard":
                        package_name = info.get("A").split(":")[1]
                        return package_name
                    else:
                        raise Exception("new foreground app type")
            for line in raw_result.splitlines():
                if "mResumedActivity: " in line:
                    for word in line.split(" "):
                        if "/" in word:
                            package_name = word.split("/")[0]
                            return package_name
                    break
            return "OFF"
        except Exception:
            log(
                self.main_logger,
                "error",
                f"new foreground app result\n{format_exc()}",
                True,
                screenshot=self.take_screenshot(),
                command_result=raw_result,
            )
            return None

    def uninstall_app(self, package_name: str):
        self.exec_android_sh_command(f"shell pm uninstall --user 0 {package_name}")

    def inoperative_wait(self, seconds: int = 1800):
        log(
            self.main_logger,
            "error",
            f"reached a consecutive/critical error on device {self.device_name}, waiting for {seconds} seconds",
        )
        while True:
            time.sleep(seconds)
            if self.get_own_state():
                self.running = True
                return

    def run(self):
        t1 = threading.Thread(target=self.start_device)
        t1.start()
        return t1

    def pull_backup(self, package_name, remote_path: str):
        """
        Pulls backup files/folders from the device.

        Args:
            package_name (str): The package name of the app that was backed up
            remote_path (str): Custom path on the device.

        Returns:
            tuple: (data, metadata) where:
                  - data is the compressed archive bytes
                  - metadata contains information about the backup
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            metadata = {
                "package_name": package_name,
                "device_id": self.device_id,
                "device_name": self.device_name,
                "timestamp": timestamp,
                "source": "neo_backup",
                "number": "",
            }

            self.main_logger.info(
                f"Pulling backup for {package_name} from {remote_path}"
            )

            # Check if remote path exists
            if "No such file" in self.exec_android_sh_command(
                f"shell ls {remote_path}", read_output=True
            ):
                self.main_logger.error(
                    f"Backup path {remote_path} does not exist on device"
                )
                raise Exception(f"Backup path {remote_path} does not exist on device")

            with tempfile.TemporaryDirectory() as temp_dir:
                local_dir = os.path.join(temp_dir, f"{package_name}_{timestamp}")
                os.makedirs(local_dir, exist_ok=True)

                # Pull files from device
                self.exec_android_sh_command(
                    f"pull {remote_path} {local_dir}", timeout=300
                )

                if not os.path.exists(local_dir) or not os.listdir(local_dir):
                    self.main_logger.error(f"Failed to pull backup from {remote_path}")
                    raise Exception(f"Failed to pull backup from {remote_path}")

                self.main_logger.info(f"Successfully pulled backup to {local_dir}")

                # Determine base directory
                pulled_path = (
                    os.path.join(local_dir, os.path.basename(remote_path))
                    if os.path.isdir(
                        os.path.join(local_dir, os.path.basename(remote_path))
                    )
                    else local_dir
                )

                # Create compressed archive
                archive_path = os.path.join(temp_dir, f"{package_name}_{timestamp}")
                shutil.make_archive(archive_path, "zip", pulled_path)
                zip_path = f"{archive_path}.zip"

                if not os.path.exists(zip_path):
                    self.main_logger.error(
                        f"Failed to create zip archive at {zip_path}"
                    )
                    raise Exception(f"Failed to create zip archive at {zip_path}")

                self.main_logger.info(
                    f"Successfully created compressed backup at {zip_path}"
                )
                metadata.update(
                    {
                        "archive_name": f"{package_name}_{timestamp}.zip",
                        "base_path": os.path.basename(remote_path),
                    }
                )

                with open(zip_path, "rb") as f:
                    return f.read(), metadata

        except Exception as e:
            self.main_logger.error(f"Error in pull_backup: {str(e)}")
            raise e

    def pull_and_upload_backup(self, package_name, remote_path: str):
        """
        Pulls backup files/folders from the device, compresses them, and uploads to an endpoint.

        Args:
            package_name (str): The package name of the app that was backed up
            remote_path (str): Custom path on the device.

        Returns:
            bool: True if successful, False otherwise
        """
        archive_data, metadata = self.pull_backup(package_name, remote_path)
        return process_and_upload_backup(archive_data, metadata)

    def clean_directory(self, path: str):
        """
        Cleans a directory by removing all files and subdirectories on the Android device.

        Args:
            path (str): The path to the directory to clean on the device

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if "No such file" in self.exec_android_sh_command(
                f"shell ls {path}", read_output=True
            ):
                self.main_logger.error(f"Directory {path} does not exist on device")
                return False

            self.exec_android_sh_command(f"shell rm -rf {path}/*")

            if self.exec_android_sh_command(
                f"shell ls {path}", read_output=True
            ).strip():
                self.main_logger.error(f"Failed to clean directory {path}")
                return False

            self.main_logger.info(f"Successfully cleaned directory {path}")
            return True

        except Exception as e:
            self.main_logger.error(f"Error cleaning directory {path}: {str(e)}")
            return False

    def push_file(self, local_path: str, remote_path: str, override: bool = False):
        """
        Pushes a file or folder to the Android device.

        Args:
            local_path (str): Path to the local file/folder to push
            remote_path (str): Destination path on the device
            override (bool): If True, removes existing file/folder before pushing

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if override and "No such file" not in self.exec_android_sh_command(
                f"shell ls {remote_path}", read_output=True
            ):
                self.exec_android_sh_command(f"shell rm -rf {remote_path}")

            self.exec_android_sh_command(
                f"push {local_path} {remote_path}", timeout=300
            )

            if "No such file" in self.exec_android_sh_command(
                f"shell ls {remote_path}", read_output=True
            ):
                self.main_logger.error(f"Failed to push {local_path} to {remote_path}")
                return False

            self.main_logger.info(f"Successfully pushed {local_path} to {remote_path}")
            return True

        except Exception as e:
            self.main_logger.error(f"Error pushing file: {str(e)}")
            return False

    def apply_optimizations(self):
        try:
            self.exec_android_sh_command(
                "shell settings put global window_animation_scale 0"
            )
            self.exec_android_sh_command(
                "shell settings put global transition_animation_scale 0"
            )
            self.exec_android_sh_command(
                "shell settings put global animator_duration_scale 0"
            )
            self.exec_android_sh_command(
                "shell settings put global stay_on_while_plugged_in 1"
            )
            self.exec_android_sh_command(
                "shell settings put system accelerometer_rotation 0"
            )
            self.exec_android_sh_command(
                "shell settings put global always_finish_activities 1"
            )
            self.exec_android_sh_command(
                "shell settings put global anr_show_background 0"
            )
        except Exception as e:
            self.main_logger.debug(f"Error applying optimizations: {str(e)}")

    def manage_device_apps(self, device_id):
        """Manage device apps - stop services and disable packages (based on manage_adb_apps.sh)"""

        subprocess.run(
            ["adb", "-s", device_id, "root"],
            capture_output=True,
            text=True,
            timeout=60,
        )
        # Services to stop (from manage_adb_apps.sh)
        services_to_stop = [
            "logd",
            "logcat",
            "lmkd",
            "statsd",
            "audioserver",
            "camearserver",
        ]

        # Packages to disable (from manage_adb_apps.sh)
        packages_to_disable = [
            "com.android.printspooler",
            "com.android.bluetooth",
            "com.android.phone",
            "android.ext.services",
            "evs.android.app",
            "com.android.providers.calender",
        ]

        self.main_logger.info(f"Managing apps and services on {device_id}")

        # Stop services
        for service in services_to_stop:
            try:
                self.main_logger.info(f"Stopping service {service} on {device_id}")
                result = subprocess.run(
                    ["adb", "-s", device_id, "shell", "stop", service],
                    capture_output=True,
                    text=True,
                    timeout=60,
                )

                if result.returncode == 0:
                    self.main_logger.info(
                        f"Successfully stopped service {service} on {device_id}"
                    )
                else:
                    error_msg = f"Failed to stop {service}: {result.stderr.strip()}"
                    self.main_logger.error(error_msg)
            except subprocess.TimeoutExpired:
                error_msg = f"Timeout stopping service {service}"
                self.main_logger.error(error_msg)
            except Exception as e:
                error_msg = f"Error stopping {service}: {str(e)}"
                self.main_logger.error(error_msg)

        # Disable packages
        for package in packages_to_disable:
            try:
                self.main_logger.info(f"Disabling package {package} on {device_id}")
                result = subprocess.run(
                    ["adb", "-s", device_id, "shell", "pm", "disable", package],
                    capture_output=True,
                    text=True,
                    timeout=60,
                )

                if result.returncode == 0:
                    self.main_logger.info(
                        f"Successfully disabled package {package} on {device_id}"
                    )
                else:
                    error_msg = f"Failed to disable {package}: {result.stderr.strip()}"
                    self.main_logger.error(error_msg)
            except subprocess.TimeoutExpired:
                error_msg = f"Timeout disabling package {package}"
                self.main_logger.error(error_msg)
            except Exception as e:
                error_msg = f"Error disabling {package}: {str(e)}"
                self.main_logger.error(error_msg)

    def empty_ram_with_adb(self):
        try:
            self.exec_android_sh_command("root")
            self.exec_android_sh_command("echo 3 > /proc/sys/vm/drop_caches")
        except Exception as e:
            self.main_logger.warning(f"Error emptying RAM with adb: {str(e)}")
